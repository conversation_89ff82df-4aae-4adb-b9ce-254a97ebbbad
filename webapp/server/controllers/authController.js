import jwt from 'jsonwebtoken';
import { authenticator } from 'otplib';
import QRCode from 'qrcode';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import UserFarm from '../models/UserFarm.js';
import RolePermission from '../models/RolePermission.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import Role from '../models/Role.js';
import MenuPreference from '../models/MenuPreference.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import dotenv from 'dotenv';
import nodemailer from 'nodemailer';
import axios from 'axios';
import { createSession, terminateSessionByToken, isDeviceRecognized, isDeviceTrusted, trustDevice } from './sessionController.js';
import RefreshToken from '../models/RefreshToken.js';
import { generateFingerprint } from '../services/deviceFingerprintService.js';
import { validatePhoneNumber, generateVerificationCode, sendVerificationCode, send2FACode } from '../services/twilioService.js';
import { stripe } from '../config/stripe.js';

// Helper function to get the client's IP address
const getClientIp = (req) => {
  const forwardedFor = req.headers['x-forwarded-for'];
  if (forwardedFor) {
    // Get the first IP if there are multiple in the header
    return forwardedFor.split(',')[0].trim();
  }
  return req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         req.connection.socket?.remoteAddress || 
         '0.0.0.0';
};

// Helper function to get the appropriate frontend URL based on the user's farm
const getFrontendUrl = async (userId, defaultUrl = null) => {
  try {
    // If no userId is provided, return the default URL
    if (!userId) {
      return defaultUrl || process.env.FRONTEND_URL || 'https://app.nxtacre.com';
    }

    // Import models here to avoid circular dependencies
    const Farm = (await import('../models/Farm.js')).default;
    const UserFarm = (await import('../models/UserFarm.js')).default;

    // Find the user's farms
    const userFarms = await UserFarm.findAll({
      where: { user_id: userId },
      include: [
        {
          model: Farm,
          attributes: ['id', 'name', 'subdomain']
        }
      ],
      order: [['created_at', 'ASC']] // Get the oldest association first
    });

    // If user has farms with a subdomain, use the subdomain of the first one
    if (userFarms.length > 0 && userFarms[0].Farm && userFarms[0].Farm.subdomain) {
      const subdomain = userFarms[0].Farm.subdomain;
      const mainDomain = process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';

      return `https://${subdomain}.${mainDomain}`;
    }

    // Otherwise, return the default URL
    return defaultUrl || process.env.FRONTEND_URL || 'https://app.nxtacre.com';
  } catch (error) {
    console.error('Error getting frontend URL:', error);
    return defaultUrl || process.env.FRONTEND_URL || 'https://app.nxtacre.com';
  }
};

// Helper function to check if a subscription plan is a trial plan
const isTrialPlan = async (planId) => {
  if (!planId) return false;
  try {
    const plan = await SubscriptionPlan.findByPk(planId);
    return plan && plan.is_trial;
  } catch (error) {
    console.error('Error checking if plan is trial:', error);
    return false;
  }
};

// Helper function to get the cookie domain with proper formatting
const getCookieDomain = () => {
  const mainDomain = process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';
  return `.${mainDomain}`;
};

// Helper function to set secure cookies with consistent options
const setSecureCookie = (res, name, value, options = {}) => {
  // Use the default options from the middleware in index.js
  // which sets sameSite: 'none' and secure: true for cross-subdomain sharing
  return res.cookie(name, value, {
    httpOnly: true,
    domain: getCookieDomain(),
    ...options
  });
};

// Helper function to find or create a role by name and farm ID
const findOrCreateRoleByName = async (roleName, farmId, transaction) => {
  try {
    // First, try to find an existing role for this farm with this name
    let role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: farmId
      },
      transaction
    });

    // If role exists, return it
    if (role) {
      return role;
    }

    // If no farm-specific role exists, try to find a global role with this name
    role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: null
      },
      transaction
    });

    // If global role exists, return it
    if (role) {
      return role;
    }

    // If no role exists at all, create a new one for this farm
    console.log(`Creating new role '${roleName}' for farm ${farmId}`);
    role = await Role.create({
      name: roleName,
      farm_id: farmId,
      description: `${roleName} role for farm ${farmId}`,
      is_system_role: true
    }, { transaction });

    return role;
  } catch (error) {
    console.error(`Error finding or creating role '${roleName}' for farm ${farmId}:`, error);
    throw error;
  }
};

dotenv.config();

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m'; // Reduced from 1d to 15m for better security
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your_refresh_token_secret';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_PORT === '465',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

// Helper function to generate tokens
const generateTokens = async (userId, ipAddress) => {
  // Generate access token with shorter expiration
  const token = jwt.sign({ id: userId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

  // Create refresh token in database
  const refreshTokenObj = await RefreshToken.createToken(userId, ipAddress);

  return { 
    token, 
    refreshToken: refreshTokenObj.token,
    refreshTokenId: refreshTokenObj.id,
    refreshTokenExpires: refreshTokenObj.expires_at
  };
};

// Register a new user
export const register = async (req, res) => {
  console.log('Registration request received:', req.body);
  let transaction;

  try {
    transaction = await sequelize.transaction();
    console.log('Transaction started');

    const { 
      email, 
      password, 
      firstName, 
      lastName, 
      phoneNumber, 
      userType = 'farmer', 
      isBusinessOwner = false,
      farmId = null,
      farmName = null
    } = req.body;

    console.log('Registration data:', { 
      email, 
      firstName, 
      lastName, 
      phoneNumber, 
      userType,
      isBusinessOwner,
      farmId,
      farmName,
      passwordLength: password?.length 
    });

    // Validate user type
    const validUserTypes = ['farmer', 'supplier', 'vet', 'admin'];
    if (!validUserTypes.includes(userType)) {
      console.log('Invalid user type:', userType);
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid user type' });
    }

    // Check if user already exists
    console.log('Checking if user already exists with email:', email);
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      console.log('User already exists with email:', email);
      await transaction.rollback();
      return res.status(400).json({ error: 'Email already in use' });
    }
    console.log('No existing user found with email:', email);

    // Create new user
    console.log('Creating new user');
    const user = await User.create({
      email,
      password_hash: password, // Will be hashed by the model hook
      first_name: firstName,
      last_name: lastName,
      phone_number: phoneNumber,
      user_type: userType,
      is_business_owner: isBusinessOwner,
      is_approved: !isBusinessOwner // Business accounts need approval
    }, { transaction });
    console.log('User created with ID:', user.id);

    // We'll create a Stripe customer for the farm instead of the user
    // This will be done after farm creation (see below)
    // The old code created a Stripe customer for the user, but we want to use farm IDs instead

    // Create default dashboard layout for the new user
    try {
      console.log('Creating default dashboard layout for new user');
      const DashboardLayout = (await import('../models/DashboardLayout.js')).default;

      // Determine dashboard type based on user type and request preference
      let dashboardType = req.body.dashboardType || 'farm';

      // If no specific preference, use type based on user type
      if (!req.body.dashboardType) {
        if (userType === 'supplier' || userType === 'vet' || isBusinessOwner) {
          dashboardType = 'business';
        } else {
          dashboardType = 'farm';
        }
      }

      console.log(`Using dashboard type: ${dashboardType} for user ${user.id}`);

      // Get the dashboard layout from the controller
      const dashboardController = await import('../controllers/dashboardController.js');
      const layoutConfig = dashboardController.getDefaultDashboardLayout(dashboardType);

      // Create the dashboard layout
      const dashboardLayout = await DashboardLayout.create({
        user_id: user.id,
        layout_config: layoutConfig,
        dashboard_type: dashboardType // Store the dashboard type
      }, { transaction });

      if (!dashboardLayout) {
        throw new Error('Failed to create dashboard layout - returned null or undefined');
      }

      console.log('Default dashboard layout created successfully with ID:', dashboardLayout.id);
    } catch (dashboardError) {
      console.error('Error creating default dashboard layout:', dashboardError);
      // Log the error but don't fail the transaction - we'll handle this in a separate process
      // This ensures user registration can complete even if dashboard creation has an issue
    }

    // Create default menu preferences for the new user
    try {
      console.log('Creating default menu preferences for new user');

      // Get default menu structure based on user role
      const getDefaultMenuStructure = (role = null) => {
        // Common header items for all users
        const commonHeaderItems = [
          { id: 'dashboard', title: 'Dashboard', path: '/dashboard', category: 'header', isRequired: false, isVisible: true },
          { id: 'tasks', title: 'Tasks', path: '/tasks', category: 'header', isRequired: false, isVisible: true },
        ];

        // Common sidebar categories for all users
        const commonSidebarCategories = [
          {
            id: 'dashboard-analytics',
            title: 'Dashboard & Analytics',
            items: [
              { id: 'dashboard-sidebar', title: 'Dashboard', path: '/dashboard', category: 'dashboard-analytics', isRequired: false, isVisible: true },
              { id: 'reports', title: 'Reports', path: '/reports', category: 'dashboard-analytics', isRequired: false, isVisible: true },
            ]
          },
          {
            id: 'resource-management',
            title: 'Resource Management',
            items: [
              { id: 'inventory', title: 'Inventory', path: '/inventory', category: 'resource-management', isRequired: false, isVisible: true },
            ]
          }
        ];

        // Common quick links for all users
        const commonQuickLinksItems = [
          { id: 'inventory-quick', title: 'Inventory', path: '/inventory', category: 'quick-links', isRequired: false, isVisible: true },
          { id: 'transactions-quick', title: 'Transactions', path: '/transactions', category: 'quick-links', isRequired: false, isVisible: true },
        ];

        // Role-specific menu items
        let headerItems = [...commonHeaderItems];
        let sidebarCategories = [...commonSidebarCategories];
        let quickLinksItems = [...commonQuickLinksItems];

        // First, add items based on user type
        switch (userType) {
          case 'farmer':
            headerItems.push(
              { id: 'hr', title: 'HR', path: '/hr', category: 'header', isRequired: false, isVisible: true },
              { id: 'market-prices-header', title: 'Market Prices', path: '/market-prices', category: 'header', isRequired: false, isVisible: true },
              { id: 'weather-header', title: 'Weather', path: '/weather', category: 'header', isRequired: false, isVisible: true },
              { id: 'suppliers-header', title: 'Suppliers', path: '/suppliers', category: 'header', isRequired: false, isVisible: true }
            );

            // Add farmer-specific sidebar categories
            sidebarCategories.push(
              {
                id: 'financial-management',
                title: 'Financial Management',
                items: [
                  { id: 'transactions', title: 'Transactions', path: '/transactions', category: 'financial-management', isRequired: false, isVisible: true },
                  { id: 'link-account', title: 'Link Account', path: '/link-account', category: 'financial-management', isRequired: false, isVisible: true },
                  { id: 'farms', title: 'Farms', path: '/farms', category: 'financial-management', isRequired: false, isVisible: true },
                  { id: 'customers', title: 'Customers', path: '/customers', category: 'financial-management', isRequired: false, isVisible: true },
                  { id: 'invoices', title: 'Invoices', path: '/invoices', category: 'financial-management', isRequired: false, isVisible: true },
                  { id: 'products', title: 'Products', path: '/products', category: 'financial-management', isRequired: false, isVisible: true },
                ]
              },
              {
                id: 'farm-operations',
                title: 'Farm Operations',
                items: [
                  { id: 'fields', title: 'Fields', path: '/fields', category: 'farm-operations', isRequired: false, isVisible: true },
                  { id: 'crops', title: 'Crops', path: '/crops', category: 'farm-operations', isRequired: false, isVisible: true },
                  { id: 'crop-types', title: 'Crop Types', path: '/crop-types', category: 'farm-operations', isRequired: false, isVisible: true },
                  { id: 'livestock', title: 'Livestock', path: '/livestock', category: 'farm-operations', isRequired: false, isVisible: true },
                  { id: 'weather', title: 'Weather System', path: '/weather', category: 'farm-operations', isRequired: false, isVisible: true },
                  { id: 'mobile-features', title: 'Mobile Features', path: '/mobile-features', category: 'farm-operations', isRequired: false, isVisible: true },
                ]
              }
            );

            // Add farmer-specific quick links
            quickLinksItems.push(
              { id: 'fields-quick', title: 'Fields', path: '/fields', category: 'quick-links', isRequired: false, isVisible: true },
              { id: 'crop-types-quick', title: 'Crop Types', path: '/crop-types', category: 'quick-links', isRequired: false, isVisible: true },
              { id: 'mobile-features-quick', title: 'Mobile Features', path: '/mobile-features', category: 'quick-links', isRequired: false, isVisible: true }
            );
            break;

          case 'admin':
            headerItems.push(
              { id: 'hr', title: 'HR', path: '/hr', category: 'header', isRequired: false, isVisible: true },
              { id: 'admin-header', title: 'Admin', path: '/admin', category: 'header', isRequired: false, isVisible: true }
            );

            // Add admin-specific sidebar categories
            sidebarCategories.push(
              {
                id: 'admin-panel',
                title: 'Administration',
                items: [
                  { id: 'user-management', title: 'User Management', path: '/admin/users', category: 'admin-panel', isRequired: false, isVisible: true },
                  { id: 'farm-management', title: 'Farm Management', path: '/admin/farms', category: 'admin-panel', isRequired: false, isVisible: true },
                  { id: 'system-settings', title: 'System Settings', path: '/admin/settings', category: 'admin-panel', isRequired: false, isVisible: true },
                ]
              }
            );

            // Add admin-specific quick links
            quickLinksItems.push(
              { id: 'user-management-quick', title: 'User Management', path: '/admin/users', category: 'quick-links', isRequired: false, isVisible: true },
              { id: 'system-settings-quick', title: 'System Settings', path: '/admin/settings', category: 'quick-links', isRequired: false, isVisible: true }
            );
            break;

          case 'accountant':
            headerItems.push(
              { id: 'finance-header', title: 'Finance', path: '/finance', category: 'header', isRequired: false, isVisible: true }
            );

            // Add accountant-specific sidebar categories
            sidebarCategories.push(
              {
                id: 'financial-management',
                title: 'Financial Management',
                items: [
                  { id: 'transactions', title: 'Transactions', path: '/transactions', category: 'financial-management', isRequired: false, isVisible: true },
                  { id: 'invoices', title: 'Invoices', path: '/invoices', category: 'financial-management', isRequired: false, isVisible: true },
                  { id: 'financial-reports', title: 'Financial Reports', path: '/financial-reports', category: 'financial-management', isRequired: false, isVisible: true },
                  { id: 'tax-documents', title: 'Tax Documents', path: '/tax-documents', category: 'financial-management', isRequired: false, isVisible: true },
                ]
              }
            );

            // Add accountant-specific quick links
            quickLinksItems.push(
              { id: 'financial-reports-quick', title: 'Financial Reports', path: '/financial-reports', category: 'quick-links', isRequired: false, isVisible: true },
              { id: 'tax-documents-quick', title: 'Tax Documents', path: '/tax-documents', category: 'quick-links', isRequired: false, isVisible: true }
            );
            break;

          // For supplier and vet, we'll use the business menu structure defined in registerBusiness
          // This is just a fallback in case they're created through this function
          case 'supplier':
          case 'vet':
            headerItems.push(
              { id: 'business-header', title: 'Business', path: '/business', category: 'header', isRequired: false, isVisible: true }
            );

            // Add business-specific sidebar categories
            sidebarCategories.push(
              {
                id: 'business-management',
                title: 'Business Management',
                items: [
                  { id: 'business-profile', title: 'Business Profile', path: '/business-profile', category: 'business-management', isRequired: false, isVisible: true },
                  { id: 'products-services', title: 'Products & Services', path: '/products-services', category: 'business-management', isRequired: false, isVisible: true },
                  { id: 'customers-business', title: 'Customers', path: '/customers-business', category: 'business-management', isRequired: false, isVisible: true },
                ]
              }
            );

            // Add business-specific quick links
            quickLinksItems.push(
              { id: 'business-profile-quick', title: 'Business Profile', path: '/business-profile', category: 'quick-links', isRequired: false, isVisible: true },
              { id: 'products-services-quick', title: 'Products & Services', path: '/products-services', category: 'quick-links', isRequired: false, isVisible: true }
            );
            break;
        }

        // Then, add or modify items based on role level
        if (role) {
          const roleName = role.name.toLowerCase();

          // Add role-specific menu items
          if (roleName === 'farm_owner' || roleName === 'farm_admin') {
            // Farm owners and admins get full access to all farm management features
            sidebarCategories.push({
              id: 'farm-management',
              title: 'Farm Management',
              items: [
                { id: 'farm-settings', title: 'Farm Settings', path: '/farm-settings', category: 'farm-management', isRequired: false, isVisible: true },
                { id: 'user-permissions', title: 'User Permissions', path: '/user-permissions', category: 'farm-management', isRequired: false, isVisible: true },
                { id: 'subscription', title: 'Subscription', path: '/subscription', category: 'farm-management', isRequired: false, isVisible: true },
              ]
            });

            // Add to quick links
            quickLinksItems.push(
              { id: 'farm-settings-quick', title: 'Farm Settings', path: '/farm-settings', category: 'quick-links', isRequired: false, isVisible: true }
            );
          } 
          else if (roleName === 'farm_manager') {
            // Farm managers get access to operational features but not administrative ones
            sidebarCategories.push({
              id: 'team-management',
              title: 'Team Management',
              items: [
                { id: 'team-members', title: 'Team Members', path: '/team-members', category: 'team-management', isRequired: false, isVisible: true },
                { id: 'task-assignment', title: 'Task Assignment', path: '/task-assignment', category: 'team-management', isRequired: false, isVisible: true },
              ]
            });

            // Add to quick links
            quickLinksItems.push(
              { id: 'task-assignment-quick', title: 'Task Assignment', path: '/task-assignment', category: 'quick-links', isRequired: false, isVisible: true }
            );
          }
          else if (roleName === 'farm_employee') {
            // Farm employees get limited access
            // No additional menus, they use the basic set
          }
          else if (roleName === 'accountant') {
            // Accountants get specialized financial access
            sidebarCategories.push({
              id: 'accounting',
              title: 'Accounting',
              items: [
                { id: 'ledger', title: 'Ledger', path: '/ledger', category: 'accounting', isRequired: false, isVisible: true },
                { id: 'tax-management', title: 'Tax Management', path: '/financial-management/tax', category: 'accounting', isRequired: false, isVisible: true },
              ]
            });

            // Add to quick links
            quickLinksItems.push(
              { id: 'ledger-quick', title: 'Ledger', path: '/ledger', category: 'quick-links', isRequired: false, isVisible: true }
            );
          }
        }

        return {
          headerItems,
          sidebarCategories,
          quickLinksItems
        };
      };

      // Create the menu preferences with basic structure
      const menuPreferences = await MenuPreference.create({
        user_id: user.id,
        preferences: {
          userId: user.id,
          ...getDefaultMenuStructure()
        }
      }, { transaction });

      if (!menuPreferences) {
        throw new Error('Failed to create menu preferences - returned null or undefined');
      }

      console.log('Default menu preferences created successfully with ID:', menuPreferences.id);
    } catch (menuError) {
      console.error('Error creating default menu preferences:', menuError);
      // Log the error but don't fail the transaction - we'll handle this in a separate process
      // This ensures user registration can complete even if menu preferences creation has an issue
    }

    // If user is a business owner (supplier or vet), create the corresponding record
    if (isBusinessOwner) {
      console.log(`Creating ${userType} record for business owner`);

      if (userType === 'supplier') {
        // Create a supplier record
        const Supplier = (await import('../models/Supplier.js')).default;
        await Supplier.create({
          user_id: user.id,
          name: `${firstName} ${lastName}'s Business`,
          contact_name: `${firstName} ${lastName}`,
          email: email,
          phone: phoneNumber,
          is_active: true
        }, { transaction });
        console.log('Supplier record created');
      } else if (userType === 'vet') {
        // Create a vet record if the model exists
        try {
          const Vet = (await import('../models/Vet.js')).default;
          await Vet.create({
            user_id: user.id,
            name: `${firstName} ${lastName}'s Veterinary Practice`,
            contact_name: `${firstName} ${lastName}`,
            email: email,
            phone: phoneNumber,
            is_active: true
          }, { transaction });
          console.log('Vet record created');
        } catch (error) {
          console.log('Vet model not found or error creating vet record:', error.message);
          // Continue with user creation even if vet record creation fails
        }
      }
    }

        // Handle farm creation and role assignment
        let createdFarmId = null;

        // If user is a farmer, check if they already have a farm before creating a new one
        if (userType === 'farmer') {
          console.log('Checking if user already has a farm');

          // Check if user already has a farm association
          const existingUserFarm = await UserFarm.findOne({
            where: { user_id: user.id },
            transaction
          });

          if (existingUserFarm) {
            console.log('User already has a farm with ID:', existingUserFarm.farm_id);
            createdFarmId = existingUserFarm.farm_id;
          } else {
            console.log('Creating farm for farmer');

            // Get the subdomain from request if provided
            const subdomain = req.body.subdomain;

            // Create farm with default name if not provided
            const farm = await Farm.create({
              name: farmName || `${firstName} ${lastName}'s Farm`,
              subscription_status: 'active',
              subscription_start_date: new Date(),
              billing_email: email,
              subdomain: subdomain
            }, { transaction });

            console.log('Farm created with ID:', farm.id);
            createdFarmId = farm.id;

            // Create Stripe customer for the new farm
            try {
              console.log('Creating Stripe customer for new farm');
              const customer = await stripe.customers.create({
                email: farm.billing_email || user.email,
                name: farm.name,
                phone: user.phone_number,
                metadata: {
                  farm_id: farm.id,
                  owner_id: user.id,
                  owner_name: `${user.first_name} ${user.last_name}`
                }
              });
              console.log('Stripe customer created with ID:', customer.id);

              // Save the Stripe customer ID to the farm record
              await farm.update({ stripe_customer_id: customer.id }, { transaction });
              console.log('Stripe customer ID saved to farm record');
            } catch (stripeError) {
              console.error('Error creating Stripe customer for farm:', stripeError);
              // Continue with farm creation even if Stripe customer creation fails
              // We can retry creating the Stripe customer later if needed
            }
          }

          let farmId, ownerRole;

          if (!existingUserFarm) {
            farmId = farm.id;
            // Find or create the farm_owner role
            ownerRole = await findOrCreateRoleByName('farm_owner', farmId, transaction);

            // Create UserFarm association with farm_owner role
            const userFarm = await UserFarm.create({
              user_id: user.id,
              farm_id: farmId,
              role: 'farm_owner',
              role_id: ownerRole.id,
              is_approved: true,
              is_billing_contact: true
            }, { transaction });

            console.log('UserFarm association created with farm_owner role');
          } else {
            farmId = existingUserFarm.farm_id;
            console.log('User already has a farm association, skipping creation');
          }

          // Update menu preferences based on the assigned role
          try {
            console.log('Updating menu preferences based on farm_owner role');
            // Import the menu utility function
            const { updateMenuPreferencesByRole } = await import('../utils/menuUtils.js');

            // If we have an existing user farm, we need to get the role
            let roleToUse;
            if (existingUserFarm) {
              roleToUse = await Role.findByPk(existingUserFarm.role_id, { transaction });
            } else if (ownerRole) {
              roleToUse = ownerRole;
            } else {
              // If we don't have a role yet, find or create one
              roleToUse = await findOrCreateRoleByName('farm_owner', farmId, transaction);
            }

            // Update menu preferences with the utility function
            await updateMenuPreferencesByRole(user.id, roleToUse, transaction);
            console.log('Menu preferences updated with role-specific items');
          } catch (menuUpdateError) {
            console.error('Error updating menu preferences with role-specific items:', menuUpdateError);
            // Don't fail the transaction if this fails
          }

          // Create default role permissions for the farm only if we created a new farm
          if (!existingUserFarm) {
            const defaultRoles = ['farm_owner', 'farm_admin', 'farm_manager', 'farm_employee', 'accountant'];
            const defaultFeatures = [
              'dashboard', 'fields', 'crops', 'inventory', 'equipment', 
              'livestock', 'finances', 'reports', 'settings', 'users'
            ];

            // Create permissions for each role and feature
            for (const role of defaultRoles) {
              for (const feature of defaultFeatures) {
                // Farm owner and admin have full access
                const isOwnerOrAdmin = role === 'farm_owner' || role === 'farm_admin';
                // Managers have create/edit/view access
                const isManager = role === 'farm_manager';
                // Employees have view access only
                const isEmployee = role === 'farm_employee';
                // Accountants have view access to finances only
                const isAccountant = role === 'accountant';
                const hasAccess = isOwnerOrAdmin || isManager || isEmployee || (isAccountant && feature === 'finances');

                if (hasAccess) {
                  // Find or create the role to get its ID
                  const roleObj = await findOrCreateRoleByName(role, farmId, transaction);

                  await RolePermission.create({
                    farm_id: farmId,
                    role_id: roleObj.id,
                    role_name: role,
                    feature: feature,
                    can_view: true,
                    can_create: isOwnerOrAdmin || isManager || (isAccountant && feature === 'finances'),
                    can_edit: isOwnerOrAdmin || isManager || (isAccountant && feature === 'finances'),
                    can_delete: isOwnerOrAdmin
                  }, { transaction });
                }
              }
            }
          }

          console.log('Default role permissions created for farm');
        } 
        // If user is an employee and farmId is provided, assign the farm_employee role
        else if (farmId) {
          console.log('Assigning farm_employee role for existing farm');

          // Check if farm exists
          const farm = await Farm.findByPk(farmId, { transaction });
          if (!farm) {
            console.log('Farm not found with ID:', farmId);
            await transaction.rollback();
            return res.status(404).json({ error: 'Farm not found' });
          }

          // Find or create the farm_employee role
          const employeeRole = await findOrCreateRoleByName('farm_employee', farmId, transaction);

          // Create UserFarm association with farm_employee role
          const userFarm = await UserFarm.create({
            user_id: user.id,
            farm_id: farmId,
            role: 'farm_employee',
            role_id: employeeRole.id,
            is_approved: false // Requires approval from farm owner or admin
          }, { transaction });

          console.log('UserFarm association created with farm_employee role');

          // Update menu preferences based on the assigned role
          try {
            console.log('Updating menu preferences based on farm_employee role');
            // Import the menu utility function
            const { updateMenuPreferencesByRole } = await import('../utils/menuUtils.js');

            // Update menu preferences with the utility function
            await updateMenuPreferencesByRole(user.id, employeeRole, transaction);
            console.log('Menu preferences updated with role-specific items');
          } catch (menuUpdateError) {
            console.error('Error updating menu preferences with role-specific items:', menuUpdateError);
            // Don't fail the transaction if this fails
          }
          createdFarmId = farmId;
        }

        console.log('Committing transaction');
        await transaction.commit();
        console.log('Transaction committed successfully');

    // Create a free trial subscription for the new user
    try {
      console.log('Creating free trial subscription for new user');
      const API_URL = process.env.API_URL || 'http://localhost:3000/api';
      await axios.post(`${API_URL}/subscriptions/users/trial`, {
        userId: user.id,
        trialDays: 30 // Default trial period is 30 days
      });
      console.log('Free trial subscription created successfully');

      // The trial information is already set by the subscription controller
      // We need to refresh the user data to get the updated trial information
      const updatedUser = await User.findByPk(user.id);
      if (updatedUser) {
        user.subscription_plan_id = updatedUser.subscription_plan_id;

        // If a farm was created, update it with the subscription plan ID
        if (createdFarmId) {
          console.log('Updating farm with subscription plan ID:', user.subscription_plan_id);
          await Farm.update(
            { subscription_plan_id: user.subscription_plan_id },
            { where: { id: createdFarmId } }
          );
        }
      }
    } catch (trialError) {
      console.error('Error creating free trial subscription:', trialError);
      // Continue with user registration even if trial creation fails
    }

    // Generate tokens
    console.log('Generating tokens for user ID:', user.id);
    const ipAddress = getClientIp(req);
    const { token, refreshToken } = await generateTokens(user.id, ipAddress);
    console.log('Tokens generated successfully');

    // Generate device fingerprint and trust the device automatically
    console.log('Automatically trusting user device upon registration');
    const deviceFingerprint = generateFingerprint(req);
    await trustDevice(user.id, deviceFingerprint);
    console.log('Device trusted successfully');

    // Check if user has a trial plan
    const isTrial = await isTrialPlan(user.subscription_plan_id);

    // Send verification and welcome emails
    try {
      console.log('Sending verification email to user');

      // Generate verification token
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const verificationTokenHash = crypto.createHash('sha256').update(verificationToken).digest('hex');

      // Save verification token to user
      user.email_verification_token = verificationTokenHash;
      user.email_verification_expires = new Date(Date.now() + ********); // 24 hours
      await user.save();

      // Create verification URL with farm-specific subdomain if available
      const frontendUrl = await getFrontendUrl(user.id);
      const verificationUrl = `${frontendUrl}/verify-email/${verificationToken}`;

      // Generate a random verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

      // Read verification email template
      const verificationTemplatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', 'email-validation.html');
      let verificationTemplate = fs.readFileSync(verificationTemplatePath, 'utf8');

      // Replace placeholders in verification template
      verificationTemplate = verificationTemplate
        .replace(/{{firstName}}/g, user.first_name)
        .replace(/{{verificationUrl}}/g, verificationUrl)
        .replace(/{{verificationCode}}/g, verificationCode)
        .replace(/{{email}}/g, user.email)
        .replace(/{{year}}/g, new Date().getFullYear());

      // Send verification email
      const verificationMailOptions = {
        from: process.env.EMAIL_FROM,
        to: user.email,
        subject: 'Verify Your Email Address',
        html: verificationTemplate,
      };

      await transporter.sendMail(verificationMailOptions);
      console.log('Verification email sent successfully');

      // Send welcome email
      console.log('Sending welcome email to user');

      // Read welcome email template
      const welcomeTemplatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', 'welcome.html');
      let welcomeTemplate = fs.readFileSync(welcomeTemplatePath, 'utf8');

      // Replace placeholders in welcome template
      welcomeTemplate = welcomeTemplate
        .replace(/{{firstName}}/g, user.first_name)
        .replace(/{{email}}/g, user.email)
        .replace(/{{userType}}/g, user.user_type.charAt(0).toUpperCase() + user.user_type.slice(1))
        .replace(/{{loginUrl}}/g, `${await getFrontendUrl(user.id)}/login`)
        .replace(/{{year}}/g, new Date().getFullYear());

      // Send welcome email
      const welcomeMailOptions = {
        from: process.env.EMAIL_FROM,
        to: user.email,
        subject: 'Welcome to NxtAcre',
        html: welcomeTemplate,
      };

      await transporter.sendMail(welcomeMailOptions);
      console.log('Welcome email sent successfully');
    } catch (emailError) {
      console.error('Error sending emails:', emailError);
      // Continue with registration even if email sending fails
    }

    // Return user info and tokens
    console.log('Registration successful, returning user data and tokens');
    return res.status(201).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        is_trial: isTrial,
        subscription_plan_id: user.subscription_plan_id,
        emailVerified: user.email_verified,
        farm_id: createdFarmId, // Include the farm ID if a farm was created
        matrix_token: user.matrix_token
      },
      token,
      refreshToken,
      message: 'Registration successful! Please check your email to verify your account.'
    });
  } catch (error) {
    console.error('Registration error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code,
      errors: error.errors
    });

    if (transaction) {
      try {
        console.log('Rolling back transaction due to error');
        await transaction.rollback();
        console.log('Transaction rolled back successfully');
      } catch (rollbackError) {
        console.error('Error rolling back transaction:', rollbackError);
      }
    }

    return res.status(500).json({ error: error.message });
  }
};

// Register a new business account (supplier or vet)
export const registerBusiness = async (req, res) => {
  console.log('Business registration request received:', req.body);
  let transaction;

  try {
    transaction = await sequelize.transaction();
    console.log('Transaction started');

    const { 
      email, 
      password, 
      firstName, 
      lastName, 
      phoneNumber, 
      userType, 
      businessDetails 
    } = req.body;

    console.log('Business registration data:', { 
      email, 
      firstName, 
      lastName, 
      phoneNumber, 
      userType,
      businessDetails,
      passwordLength: password?.length 
    });

    // Validate user type
    const validUserTypes = ['supplier', 'vet'];
    if (!validUserTypes.includes(userType)) {
      console.log('Invalid user type for business account:', userType);
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid user type for business account. Must be supplier or vet' });
    }

    // Check if user already exists
    console.log('Checking if user already exists with email:', email);
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      console.log('User already exists with email:', email);
      await transaction.rollback();
      return res.status(400).json({ error: 'Email already in use' });
    }
    console.log('No existing user found with email:', email);

    // Create new user with business owner flag and pending approval
    console.log('Creating new business user');
    const user = await User.create({
      email,
      password_hash: password, // Will be hashed by the model hook
      first_name: firstName,
      last_name: lastName,
      phone_number: phoneNumber,
      user_type: userType,
      is_business_owner: true,
      is_approved: false, // Business accounts need approval
      subscription_plan_id: null // No subscription plan initially, only access to manage business listing
    }, { transaction });
    console.log('Business user created with ID:', user.id);

    // Create Stripe customer for the new business user
    // Business users don't have farms, so we create a Stripe customer for the business user directly
    try {
      console.log('Creating Stripe customer for new business user');
      const customer = await stripe.customers.create({
        email: user.email,
        name: businessDetails.name || `${user.first_name} ${user.last_name}'s Business`,
        phone: user.phone_number,
        metadata: {
          user_id: user.id,
          user_type: user.user_type,
          business_type: userType,
          is_business: 'true'
        }
      });
      console.log('Stripe customer created with ID:', customer.id);

      // Save the Stripe customer ID to the user record
      user.stripe_customer_id = customer.id;
      await user.save({ transaction });
      console.log('Stripe customer ID saved to business user record');
    } catch (stripeError) {
      console.error('Error creating Stripe customer for business user:', stripeError);
      // Continue with business user registration even if Stripe customer creation fails
      // We can retry creating the Stripe customer later if needed
    }

    // Create default dashboard layout for the new business user
    try {
      console.log('Creating default dashboard layout for new business user');
      const DashboardLayout = (await import('../models/DashboardLayout.js')).default;

      // Determine dashboard type based on request preference or default to business
      let dashboardType = req.body.dashboardType || 'business';

      console.log(`Using dashboard type: ${dashboardType} for business user ${user.id}`);

      // Get the dashboard layout from the controller
      const dashboardController = await import('../controllers/dashboardController.js');
      const layoutConfig = dashboardController.getDefaultDashboardLayout(dashboardType);

      // Create the dashboard layout
      const dashboardLayout = await DashboardLayout.create({
        user_id: user.id,
        layout_config: layoutConfig,
        dashboard_type: dashboardType // Store the dashboard type
      }, { transaction });

      if (!dashboardLayout) {
        throw new Error('Failed to create dashboard layout for business user - returned null or undefined');
      }

      console.log('Default dashboard layout created successfully for business user with ID:', dashboardLayout.id);
    } catch (dashboardError) {
      console.error('Error creating default dashboard layout for business user:', dashboardError);
      // Log the error but don't fail the transaction - we'll handle this in a separate process
      // This ensures user registration can complete even if dashboard creation has an issue
    }

    // Create default menu preferences for the new business user
    try {
      console.log('Creating default menu preferences for new business user');

      // Get default menu structure based on user role
      const getDefaultMenuStructure = (role = null) => {
        // Common header items for all business users
        const commonHeaderItems = [
          { id: 'dashboard', title: 'Dashboard', path: '/dashboard', category: 'header', isRequired: false, isVisible: true },
          { id: 'tasks', title: 'Tasks', path: '/tasks', category: 'header', isRequired: false, isVisible: true },
          { id: 'business-header', title: 'Business', path: '/business', category: 'header', isRequired: false, isVisible: true }
        ];

        // Common sidebar categories for all business users
        const commonSidebarCategories = [
          {
            id: 'dashboard-analytics',
            title: 'Dashboard & Analytics',
            items: [
              { id: 'dashboard-sidebar', title: 'Dashboard', path: '/dashboard', category: 'dashboard-analytics', isRequired: false, isVisible: true },
              { id: 'reports', title: 'Reports', path: '/reports', category: 'dashboard-analytics', isRequired: false, isVisible: true },
            ]
          },
          {
            id: 'business-management',
            title: 'Business Management',
            items: [
              { id: 'business-profile', title: 'Business Profile', path: '/business-profile', category: 'business-management', isRequired: false, isVisible: true },
              { id: 'products-services', title: 'Products & Services', path: '/products-services', category: 'business-management', isRequired: false, isVisible: true },
              { id: 'customers-business', title: 'Customers', path: '/customers-business', category: 'business-management', isRequired: false, isVisible: true },
            ]
          },
          {
            id: 'resource-management',
            title: 'Resource Management',
            items: [
              { id: 'inventory', title: 'Inventory', path: '/inventory', category: 'resource-management', isRequired: false, isVisible: true },
            ]
          }
        ];

        // Common quick links for all business users
        const commonQuickLinksItems = [
          { id: 'business-profile-quick', title: 'Business Profile', path: '/business-profile', category: 'quick-links', isRequired: false, isVisible: true },
          { id: 'inventory-quick', title: 'Inventory', path: '/inventory', category: 'quick-links', isRequired: false, isVisible: true },
          { id: 'transactions-quick', title: 'Transactions', path: '/transactions', category: 'quick-links', isRequired: false, isVisible: true },
          { id: 'products-services-quick', title: 'Products & Services', path: '/products-services', category: 'quick-links', isRequired: false, isVisible: true },
        ];

        // Role-specific menu items
        let headerItems = [...commonHeaderItems];
        let sidebarCategories = [...commonSidebarCategories];
        let quickLinksItems = [...commonQuickLinksItems];

        // Add role-specific menu items
        switch (userType) {
          case 'supplier':
            headerItems.push(
              { id: 'market-prices-header', title: 'Market Prices', path: '/market-prices', category: 'header', isRequired: false, isVisible: true }
            );

            // Add supplier-specific sidebar categories
            sidebarCategories.push(
              {
                id: 'supplier-management',
                title: 'Supplier Management',
                items: [
                  { id: 'product-catalog', title: 'Product Catalog', path: '/product-catalog', category: 'supplier-management', isRequired: false, isVisible: true },
                  { id: 'orders', title: 'Orders', path: '/orders', category: 'supplier-management', isRequired: false, isVisible: true },
                  { id: 'shipping', title: 'Shipping', path: '/shipping', category: 'supplier-management', isRequired: false, isVisible: true },
                ]
              }
            );

            // Add supplier-specific quick links
            quickLinksItems.push(
              { id: 'product-catalog-quick', title: 'Product Catalog', path: '/product-catalog', category: 'quick-links', isRequired: false, isVisible: true },
              { id: 'orders-quick', title: 'Orders', path: '/orders', category: 'quick-links', isRequired: false, isVisible: true }
            );
            break;

          case 'vet':
            headerItems.push(
              { id: 'appointments-header', title: 'Appointments', path: '/appointments', category: 'header', isRequired: false, isVisible: true }
            );

            // Add vet-specific sidebar categories
            sidebarCategories.push(
              {
                id: 'vet-management',
                title: 'Veterinary Management',
                items: [
                  { id: 'appointments', title: 'Appointments', path: '/appointments', category: 'vet-management', isRequired: false, isVisible: true },
                  { id: 'patients', title: 'Patients', path: '/patients', category: 'vet-management', isRequired: false, isVisible: true },
                  { id: 'medical-records', title: 'Medical Records', path: '/medical-records', category: 'vet-management', isRequired: false, isVisible: true },
                  { id: 'prescriptions', title: 'Prescriptions', path: '/prescriptions', category: 'vet-management', isRequired: false, isVisible: true },
                ]
              }
            );

            // Add vet-specific quick links
            quickLinksItems.push(
              { id: 'appointments-quick', title: 'Appointments', path: '/appointments', category: 'quick-links', isRequired: false, isVisible: true },
              { id: 'patients-quick', title: 'Patients', path: '/patients', category: 'quick-links', isRequired: false, isVisible: true }
            );
            break;
        }

        // Add financial management for all business users
        sidebarCategories.push({
          id: 'financial-management',
          title: 'Financial Management',
          items: [
            { id: 'transactions', title: 'Transactions', path: '/transactions', category: 'financial-management', isRequired: false, isVisible: true },
            { id: 'invoices', title: 'Invoices', path: '/invoices', category: 'financial-management', isRequired: false, isVisible: true },
            { id: 'financial-reports', title: 'Financial Reports', path: '/financial-reports', category: 'financial-management', isRequired: false, isVisible: true },
          ]
        });

        return {
          headerItems,
          sidebarCategories,
          quickLinksItems
        };
      };

      // Create the menu preferences
      const menuPreferences = await MenuPreference.create({
        user_id: user.id,
        preferences: {
          userId: user.id,
          ...getDefaultMenuStructure()
        }
      }, { transaction });

      if (!menuPreferences) {
        throw new Error('Failed to create menu preferences for business user - returned null or undefined');
      }

      console.log('Default menu preferences created successfully for business user with ID:', menuPreferences.id);
    } catch (menuError) {
      console.error('Error creating default menu preferences for business user:', menuError);
      // Log the error but don't fail the transaction - we'll handle this in a separate process
      // This ensures user registration can complete even if menu preferences creation has an issue
    }

    // Create the corresponding business record
    if (userType === 'supplier') {
      // Create a supplier record
      const Supplier = (await import('../models/Supplier.js')).default;
      await Supplier.create({
        user_id: user.id,
        name: businessDetails.name || `${firstName} ${lastName}'s Business`,
        contact_name: `${firstName} ${lastName}`,
        email: email,
        phone: phoneNumber,
        address: businessDetails.address,
        city: businessDetails.city,
        state: businessDetails.state,
        zip_code: businessDetails.zipCode,
        country: businessDetails.country,
        website: businessDetails.website,
        description: businessDetails.description,
        product_categories: businessDetails.productCategories,
        service_areas: businessDetails.serviceAreas,
        is_active: true
      }, { transaction });
      console.log('Supplier record created');
    } else if (userType === 'vet') {
      // Create a vet record
      try {
        const Vet = (await import('../models/Vet.js')).default;
        await Vet.create({
          user_id: user.id,
          name: businessDetails.name || `${firstName} ${lastName}'s Veterinary Practice`,
          contact_name: `${firstName} ${lastName}`,
          email: email,
          phone: phoneNumber,
          address: businessDetails.address,
          city: businessDetails.city,
          state: businessDetails.state,
          zip_code: businessDetails.zipCode,
          country: businessDetails.country,
          website: businessDetails.website,
          description: businessDetails.description,
          service_areas: businessDetails.serviceAreas,
          is_active: true
        }, { transaction });
        console.log('Vet record created');
      } catch (error) {
        console.log('Vet model not found or error creating vet record:', error.message);
        // Continue with user creation even if vet record creation fails
      }
    }

    console.log('Committing transaction');
    await transaction.commit();
    console.log('Transaction committed successfully');

    // Send verification email
    try {
      console.log('Sending verification email to business user');

      // Generate verification token
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const verificationTokenHash = crypto.createHash('sha256').update(verificationToken).digest('hex');

      // Save verification token to user
      user.email_verification_token = verificationTokenHash;
      user.email_verification_expires = new Date(Date.now() + ********); // 24 hours
      await user.save();

      // Create verification URL with farm-specific subdomain if available
      const frontendUrl = await getFrontendUrl(user.id);
      const verificationUrl = `${frontendUrl}/verify-email/${verificationToken}`;

      // Generate a random verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

      // Read email template
      const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', 'email-validation.html');
      let emailTemplate = fs.readFileSync(templatePath, 'utf8');

      // Replace placeholders in template
      emailTemplate = emailTemplate
        .replace(/{{firstName}}/g, user.first_name)
        .replace(/{{verificationUrl}}/g, verificationUrl)
        .replace(/{{verificationCode}}/g, verificationCode)
        .replace(/{{email}}/g, user.email)
        .replace(/{{year}}/g, new Date().getFullYear());

      // Send email
      const mailOptions = {
        from: process.env.EMAIL_FROM,
        to: user.email,
        subject: 'Verify Your Email Address',
        html: emailTemplate,
      };

      await transporter.sendMail(mailOptions);
      console.log('Verification email sent successfully');

      // Also send business welcome email
      const businessTemplatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'business', 'new-business-account.html');
      let businessEmailTemplate = fs.readFileSync(businessTemplatePath, 'utf8');

      // Replace placeholders in template
      businessEmailTemplate = businessEmailTemplate
        .replace(/{{firstName}}/g, user.first_name)
        .replace(/{{businessName}}/g, userType === 'supplier' ? 
          businessDetails.name || `${firstName} ${lastName}'s Business` : 
          businessDetails.name || `${firstName} ${lastName}'s Veterinary Practice`)
        .replace(/{{businessType}}/g, userType === 'supplier' ? 'Supplier' : 'Veterinary Practice')
        .replace(/{{email}}/g, user.email)
        .replace(/{{loginUrl}}/g, `${await getFrontendUrl(user.id)}/login`)
        .replace(/{{year}}/g, new Date().getFullYear());

      // Send business welcome email
      const businessMailOptions = {
        from: process.env.EMAIL_FROM,
        to: user.email,
        subject: 'Welcome to NxtAcre Business',
        html: businessEmailTemplate,
      };

      await transporter.sendMail(businessMailOptions);
      console.log('Business welcome email sent successfully');
    } catch (emailError) {
      console.error('Error sending emails:', emailError);
      // Continue with registration even if email sending fails
    }

    // Return success message (no tokens for business accounts until approved)
    console.log('Business registration successful');
    return res.status(201).json({
      success: true,
      message: 'Business account registration successful. Your account is pending approval. Please check your email to verify your account.',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved,
        planLevel: user.plan_level,
        emailVerified: user.email_verified,
        matrix_token: user.matrix_token
      }
    });
  } catch (error) {
    console.error('Business registration error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code,
      errors: error.errors
    });

    if (transaction) {
      try {
        console.log('Rolling back transaction due to error');
        await transaction.rollback();
        console.log('Transaction rolled back successfully');
      } catch (rollbackError) {
        console.error('Error rolling back transaction:', rollbackError);
      }
    }

    return res.status(500).json({ error: error.message });
  }
};

// Logout user
export const logout = async (req, res) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.split(' ')[1];

      // Terminate the session in the database
      await terminateSessionByToken(token);
    }

    // Clear auth cookies with proper domain settings
    const cookieOptions = {
      domain: getCookieDomain(),
      secure: true,
      sameSite: 'none',
      httpOnly: true,
      path: '/'
    };

    res.clearCookie('auth_token', cookieOptions);
    res.clearCookie('refresh_token', cookieOptions);

    // Also clear cookies without domain (fallback for local development)
    res.clearCookie('auth_token', { secure: true, sameSite: 'none', httpOnly: true, path: '/' });
    res.clearCookie('refresh_token', { secure: true, sameSite: 'none', httpOnly: true, path: '/' });

    return res.status(200).json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({ error: 'Logout failed' });
  }
};

// Login user
export const login = async (req, res) => {
  try {
    const { email, password } = req.body;
    const { farmSubdomain } = req.body; // Get farm subdomain from request body if present
    const host = req.get('host'); // Get the host from the request headers

    // Extract subdomain from host if not provided in the request body
    let requestedSubdomain = farmSubdomain;
    if (!requestedSubdomain && host) {
      const hostParts = host.split('.');
      if (hostParts.length > 2 && hostParts[0] !== 'www' && hostParts[0] !== 'app' && hostParts[0] !== 'api') {
        requestedSubdomain = hostParts[0];
      }
    }

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Validate password
    const isPasswordValid = await user.validatePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Import models here to avoid circular dependencies
    const Farm = (await import('../models/Farm.js')).default;
    const UserFarm = (await import('../models/UserFarm.js')).default;

    // If a specific farm subdomain is requested, verify the user has access to that farm
    if (requestedSubdomain && requestedSubdomain !== 'app' && requestedSubdomain !== 'admin') {
      // Find the farm by subdomain
      const farm = await Farm.findOne({
        where: { subdomain: requestedSubdomain }
      });

      if (!farm) {
        return res.status(404).json({ error: 'Farm not found' });
      }

      // If user is not a global admin, check if they have access to this farm
      if (!user.is_global_admin) {
        // Check if user has access to this farm
        const userFarm = await UserFarm.findOne({
          where: {
            user_id: user.id,
            farm_id: farm.id
          }
        });

        if (!userFarm) {
          return res.status(403).json({ error: 'You do not have access to this farm' });
        }
      }

      // Set the farmId and farmSubdomain for the response
      const farmId = farm.id;
      const farmSubdomain = farm.subdomain;

      // Generate device fingerprint
      const deviceFingerprint = generateFingerprint(req);

      // Check if device is recognized and trusted
      const isRecognized = await isDeviceRecognized(user.id, deviceFingerprint);
      const isTrusted = await isDeviceTrusted(user.id, deviceFingerprint);

      // Check if 2FA is enabled and properly configured
      const twoFactorConfigured = user.two_factor_enabled && 
        (
          // For app-based 2FA, we need a secret
          (user.two_factor_method === 'app' && user.two_factor_secret) ||
          // For SMS-based 2FA, we need a verified phone number
          (user.two_factor_method === 'sms' && user.phone_verified && user.phone_number) ||
          // For email-based 2FA, we need a verified email
          (user.two_factor_method === 'email' && user.email_verified)
        );

      // Get all configured 2FA methods for the user
      const configuredMethods = [];

      // Check if app-based 2FA is configured
      if (user.two_factor_method === 'app' && user.two_factor_secret) {
        configuredMethods.push('app');
      }

      // Check if SMS-based 2FA is configured
      if (user.phone_verified && user.phone_number) {
        configuredMethods.push('sms');
      }

      // Check if email-based 2FA is configured
      if (user.email_verified) {
        configuredMethods.push('email');
      }

      // If device is trusted and 2FA is configured, bypass 2FA
      if (isTrusted && twoFactorConfigured) {
        console.log('Device is trusted, bypassing 2FA');
      } 
      // Otherwise, require 2FA if it's configured or if the device is not recognized or not trusted
      else if (twoFactorConfigured || !isRecognized || !isTrusted) {
        // If no 2FA methods are configured but email is verified, fall back to email 2FA
        if (!twoFactorConfigured && !configuredMethods.includes('app') && !configuredMethods.includes('sms') && configuredMethods.includes('email')) {
          console.log('No 2FA methods configured, falling back to email 2FA for unrecognized device');
          // Temporarily set up email 2FA for this login attempt
          user.two_factor_method = 'email';
          configuredMethods.push('email');
        }

        return res.status(200).json({
          requireTwoFactor: true,
          userId: user.id,
          farmId,
          farmSubdomain,
          isNewDevice: !isRecognized,
          configuredMethods: configuredMethods
        });
      }

      // Get client IP address
      const ipAddress = getClientIp(req);

      // Generate tokens with IP tracking
      const { token, refreshToken, refreshTokenExpires } = await generateTokens(user.id, ipAddress);

      // Create a session record
      await createSession(user.id, token, req);

      // Check if user has a trial plan
      const isTrial = await isTrialPlan(user.subscription_plan_id);

      // Get farm customization for login page
      const customLoginText = farm.custom_login_text;
      const customLoginLogo = farm.custom_login_logo;

      // Calculate cookie expiration times based on token expiration
      const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
      const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

      // Define mainDomain for cookie settings
      const mainDomain = process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';

      // Set auth cookies that will be available on all subdomains
      setSecureCookie(res, 'auth_token', token, {
        maxAge: accessTokenMaxAge
      });

      setSecureCookie(res, 'refresh_token', refreshToken, {
        maxAge: refreshTokenMaxAge
      });

      // Return user info, tokens, and farm info
      return res.status(200).json({
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          userType: user.user_type,
          isBusinessOwner: user.is_business_owner,
          twoFactorEnabled: user.two_factor_enabled,
          is_global_admin: user.is_global_admin,
          farm_id: farmId,
          is_trial: isTrial,
          subscription_plan_id: user.subscription_plan_id,
          matrix_token: user.matrix_token
        },
        token,
        refreshToken,
        farmSubdomain,
        farmId,
        customLoginText,
        customLoginLogo
      });
    } else {
      // No specific farm subdomain requested or using app.nxtacre.com

      // Generate device fingerprint
      const deviceFingerprint = generateFingerprint(req);

      // Check if device is recognized and trusted
      const isRecognized = await isDeviceRecognized(user.id, deviceFingerprint);
      const isTrusted = await isDeviceTrusted(user.id, deviceFingerprint);

      // Check if 2FA is enabled and properly configured, or if device is not trusted
      const twoFactorConfigured = user.two_factor_enabled && 
        (
          // For app-based 2FA, we need a secret
          (user.two_factor_method === 'app' && user.two_factor_secret) ||
          // For SMS-based 2FA, we need a verified phone number
          (user.two_factor_method === 'sms' && user.phone_verified && user.phone_number) ||
          // For email-based 2FA, we need a verified email
          (user.two_factor_method === 'email' && user.email_verified)
        );

      // Get all configured 2FA methods for the user
      const configuredMethods = [];

      // Check if app-based 2FA is configured
      if (user.two_factor_method === 'app' && user.two_factor_secret) {
        configuredMethods.push('app');
      }

      // Check if SMS-based 2FA is configured
      if (user.phone_verified && user.phone_number) {
        configuredMethods.push('sms');
      }

      // Check if email-based 2FA is configured
      if (user.email_verified) {
        configuredMethods.push('email');
      }

      // If device is trusted and 2FA is configured, bypass 2FA
      if (isTrusted && twoFactorConfigured) {
        console.log('Device is trusted, bypassing 2FA');
      }
      // Otherwise, require 2FA if it's configured or if the device is not recognized or not trusted
      else if (twoFactorConfigured || !isRecognized || !isTrusted) {
        // If no 2FA methods are configured but email is verified, fall back to email 2FA
        if (!twoFactorConfigured && !configuredMethods.includes('app') && !configuredMethods.includes('sms') && configuredMethods.includes('email')) {
          console.log('No 2FA methods configured, falling back to email 2FA for unrecognized device');
          // Temporarily set up email 2FA for this login attempt
          user.two_factor_method = 'email';
          configuredMethods.push('email');
        }

        return res.status(200).json({
          requireTwoFactor: true,
          userId: user.id,
          isNewDevice: !isRecognized,
          configuredMethods: configuredMethods
        });
      }

      // Get client IP address
      const ipAddress = getClientIp(req);

      // Generate tokens with IP tracking
      const { token, refreshToken, refreshTokenExpires } = await generateTokens(user.id, ipAddress);

      // Get user's farms and their subdomains
      let farmSubdomain = null;
      let farmId = null;

      // Check if we're on the app subdomain
      const host = req.get('host');
      const isAppSubdomain = host && host.startsWith('app.');

      // If we're on the app subdomain, keep using it and don't redirect
      if (isAppSubdomain) {
        farmSubdomain = 'app';
      } 
      // Check if we're on the admin subdomain
      else if (host && host.startsWith('admin.')) {
        farmSubdomain = 'admin';
      }
      // Global admins now use farm-specific subdomains like regular users
      else {
        // Find the user's farms
        const userFarms = await UserFarm.findAll({
          where: { user_id: user.id },
          include: [
            {
              model: Farm,
              attributes: ['id', 'name', 'subdomain']
            }
          ],
          order: [['created_at', 'ASC']] // Get the oldest association first
        });

        // If user has farms, use the subdomain of the first one
        if (userFarms.length > 0 && userFarms[0].Farm && userFarms[0].Farm.subdomain) {
          farmSubdomain = userFarms[0].Farm.subdomain;
          farmId = userFarms[0].Farm.id;
        }
      }

      // Check if user has a trial plan
      const isTrial = await isTrialPlan(user.subscription_plan_id);

      // Calculate cookie expiration times based on token expiration
      const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
      const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

      // Define mainDomain for cookie settings
      const mainDomain = process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';

      // Set auth cookies that will be available on all subdomains
      setSecureCookie(res, 'auth_token', token, {
        maxAge: accessTokenMaxAge
      });

      setSecureCookie(res, 'refresh_token', refreshToken, {
        maxAge: refreshTokenMaxAge
      });

      // Return user info, tokens, and farm subdomain
      return res.status(200).json({
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          userType: user.user_type,
          isBusinessOwner: user.is_business_owner,
          twoFactorEnabled: user.two_factor_enabled,
          is_global_admin: user.is_global_admin,
          farm_id: farmId, // Include farmId in the user object for backward compatibility
          is_trial: isTrial,
          subscription_plan_id: user.subscription_plan_id,
          matrix_token: user.matrix_token
        },
        token,
        refreshToken,
        farmSubdomain,
        farmId
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ error: error.message || 'An error occurred during login. Please try again.' });
  }
};

// Verify two-factor authentication
export const verifyTwoFactor = async (req, res) => {
  try {
    console.log('Verifying 2FA login with data:', req.body);
    const { userId, token, method, rememberDevice } = req.body;

    // Find user
    console.log('Finding user by ID:', userId);
    const user = await User.findByPk(userId);

    if (!user) {
      console.log('User not found');
      return res.status(400).json({ error: 'Invalid request' });
    }
    console.log('User found:', user.email);

    // Get all configured 2FA methods for the user
    const configuredMethods = [];

    // Check if app-based 2FA is configured
    if (user.two_factor_method === 'app' && user.two_factor_secret) {
      configuredMethods.push('app');
    }

    // Check if SMS-based 2FA is configured
    if (user.phone_verified && user.phone_number) {
      configuredMethods.push('sms');
    }

    // Check if email-based 2FA is configured
    if (user.email_verified) {
      configuredMethods.push('email');
    }

    // Check if 2FA is properly configured for the user
    const twoFactorConfigured = 
      // For app-based 2FA, we need a secret and 2FA enabled
      (user.two_factor_enabled && user.two_factor_method === 'app' && user.two_factor_secret) ||
      // For SMS-based 2FA, we need a verified phone number and 2FA enabled
      (user.two_factor_enabled && user.two_factor_method === 'sms' && user.phone_verified && user.phone_number) ||
      // For email-based 2FA, we need a verified email and 2FA enabled
      (user.two_factor_enabled && user.two_factor_method === 'email' && user.email_verified) ||
      // For fallback email 2FA, we just need a verified email (2FA doesn't need to be enabled)
      (!user.two_factor_enabled && user.email_verified);

    // If a specific method is provided, check if it's properly configured
    // Otherwise, use the user's configured method
    const verificationMethod = method || user.two_factor_method;

    // Check if the verification method is properly configured
    const isMethodConfigured = 
      (verificationMethod === 'app' && configuredMethods.includes('app')) ||
      (verificationMethod === 'sms' && configuredMethods.includes('sms')) ||
      (verificationMethod === 'email' && configuredMethods.includes('email'));

    if (!isMethodConfigured) {
      console.log('Verification method not properly configured:', verificationMethod);
      return res.status(400).json({ error: 'Selected verification method is not properly configured' });
    }

    // If the method is SMS or email, redirect to the appropriate verification endpoint
    if (verificationMethod === 'sms') {
      console.log('Redirecting to SMS 2FA verification');
      return verifySMS2FACode(req, res);
    } else if (verificationMethod === 'email') {
      console.log('Redirecting to email 2FA verification');
      return verifyEmail2FACode(req, res);
    }

    // For app-based 2FA, verify the token
    console.log('Verifying token for app-based 2FA login');
    let isValid;
    try {
      // The authenticator.verify method expects the token as the first argument and the secret as the second argument
      isValid = authenticator.verify(token, user.two_factor_secret);
      console.log('Token verification result:', isValid);
    } catch (verifyError) {
      console.error('Error verifying token:', verifyError);
      return res.status(500).json({ error: 'Error verifying authentication code' });
    }

    if (!isValid) {
      console.log('Invalid authentication code');
      return res.status(401).json({ error: 'Invalid authentication code' });
    }

    // Get client IP address
    const ipAddress = getClientIp(req);

    // Generate device fingerprint
    const deviceFingerprint = generateFingerprint(req);

    // Mark device as trusted if rememberDevice is true
    if (rememberDevice) {
      console.log('Remembering device for future 2FA bypass');
      await trustDevice(user.id, deviceFingerprint);
    }

    // Generate tokens with IP tracking
    const tokens = await generateTokens(user.id, ipAddress);

    // Get user's farms and their subdomains
    let farmSubdomain = null;
    let farmId = null;

    // Check if we're on the app subdomain
    const host = req.get('host');
    const isAppSubdomain = host && host.startsWith('app.');

    // If we're on the app subdomain, keep using it and don't redirect
    if (isAppSubdomain) {
      farmSubdomain = 'app';
    }
    // Check if we're on the admin subdomain
    else if (host && host.startsWith('admin.')) {
      farmSubdomain = 'admin';
    }
    // Global admins now use farm-specific subdomains like regular users
    else {
      // Import models here to avoid circular dependencies
      const Farm = (await import('../models/Farm.js')).default;
      const UserFarm = (await import('../models/UserFarm.js')).default;

      // Find the user's farms
      const userFarms = await UserFarm.findAll({
        where: { user_id: user.id },
        include: [
          {
            model: Farm,
            attributes: ['id', 'name', 'subdomain']
          }
        ],
        order: [['created_at', 'ASC']] // Get the oldest association first
      });

      // If user has farms, use the subdomain of the first one
      if (userFarms.length > 0 && userFarms[0].Farm && userFarms[0].Farm.subdomain) {
        farmSubdomain = userFarms[0].Farm.subdomain;
        farmId = userFarms[0].Farm.id;
      }
    }

    // Check if user has a trial plan
    const isTrial = await isTrialPlan(user.subscription_plan_id);

    // Calculate cookie expiration times based on token expiration
    const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
    const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    // Define mainDomain for cookie settings
    const mainDomain = process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';

    // Set auth cookies that will be available on all subdomains
    setSecureCookie(res, 'auth_token', tokens.token, {
      maxAge: accessTokenMaxAge
    });

    setSecureCookie(res, 'refresh_token', tokens.refreshToken, {
      maxAge: refreshTokenMaxAge
    });

    // Return user info, tokens, and farm subdomain
    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        farm_id: farmId, // Include farmId in the user object for backward compatibility
        is_trial: isTrial,
        subscription_plan_id: user.subscription_plan_id,
        matrix_token: user.matrix_token
      },
      ...tokens,
      farmSubdomain,
      farmId
    });
  } catch (error) {
    console.error('2FA verification error:', error);
    return res.status(500).json({ error: error.message || 'An error occurred during two-factor authentication. Please try again.' });
  }
};

// Setup two-factor authentication
export const setupTwoFactor = async (req, res) => {
  try {
    console.log('Setting up 2FA for user ID:', req.params.userId);
    const { userId } = req.params;

    // Find user
    console.log('Finding user by ID:', userId);
    const user = await User.findByPk(userId);
    if (!user) {
      console.log('User not found with ID:', userId);
      return res.status(404).json({ error: 'User not found' });
    }
    console.log('User found:', user.email);

    // Generate secret
    console.log('Generating 2FA secret');
    const secret = authenticator.generateSecret();
    console.log('Secret generated successfully:', secret);

    // Generate QR code
    console.log('Generating QR code');
    const otpauth = authenticator.keyuri(user.email, 'Farm Books', secret);
    console.log('OTP auth URI created');

    let qrCode;
    try {
      qrCode = await QRCode.toDataURL(otpauth);
      console.log('QR code generated successfully, length:', qrCode.length);
    } catch (qrError) {
      console.error('Error generating QR code:', qrError);
      return res.status(500).json({ error: 'Failed to generate QR code' });
    }

    // Save secret temporarily (will be confirmed later)
    console.log('Saving secret to user');
    user.two_factor_secret = secret;
    await user.save();
    console.log('Secret saved successfully to user:', user.id);

    console.log('Returning 2FA setup data');
    const response = {
      secret,
      qrCode,
    };
    console.log('Response prepared, sending to client');
    return res.status(200).json(response);
  } catch (error) {
    console.error('2FA setup error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code
    });
    return res.status(500).json({ error: error.message || 'An error occurred during two-factor authentication setup. Please try again.' });
  }
};

// Confirm two-factor authentication setup
export const confirmTwoFactor = async (req, res) => {
  try {
    console.log('Confirming 2FA setup with data:', req.body);
    const { userId, token } = req.body;

    // Find user
    console.log('Finding user by ID:', userId);
    const user = await User.findByPk(userId);
    if (!user || !user.two_factor_secret) {
      console.log('User not found or no 2FA secret');
      return res.status(400).json({ error: 'Invalid request' });
    }
    console.log('User found:', user.email);

    // Verify token
    console.log('Verifying token');
    try {
      // The authenticator.verify method expects the token as the first argument and the secret as the second argument
      const isValid = authenticator.verify(token, user.two_factor_secret);

      console.log('Token verification result:', isValid);

      if (!isValid) {
        console.log('Invalid authentication code');
        return res.status(401).json({ error: 'Invalid authentication code' });
      }
    } catch (verifyError) {
      console.error('Error verifying token:', verifyError);
      return res.status(500).json({ error: 'Error verifying authentication code' });
    }

    // Enable 2FA
    console.log('Enabling 2FA for user');
    user.two_factor_enabled = true;
    await user.save();
    console.log('2FA enabled successfully');

    console.log('Returning success response');
    return res.status(200).json({
      success: true,
      message: 'Two-factor authentication enabled successfully',
    });
  } catch (error) {
    console.error('2FA confirmation error:', error);
    return res.status(500).json({ error: error.message || 'An error occurred during two-factor authentication confirmation. Please try again.' });
  }
};

// Disable two-factor authentication
export const disableTwoFactor = async (req, res) => {
  try {
    console.log('Disabling 2FA with data:', req.body);
    const { userId, token } = req.body;

    // Find user
    console.log('Finding user by ID:', userId);
    const user = await User.findByPk(userId);
    if (!user || !user.two_factor_enabled || !user.two_factor_secret) {
      console.log('User not found, 2FA not enabled, or no 2FA secret');
      return res.status(400).json({ error: 'Invalid request' });
    }
    console.log('User found:', user.email);

    // Verify token
    console.log('Verifying token for 2FA disabling');
    let isValid;
    try {
      // The authenticator.verify method expects the token as the first argument and the secret as the second argument
      isValid = authenticator.verify(token, user.two_factor_secret);
      console.log('Token verification result:', isValid);
    } catch (verifyError) {
      console.error('Error verifying token:', verifyError);
      return res.status(500).json({ error: 'Error verifying authentication code' });
    }

    if (!isValid) {
      console.log('Invalid authentication code');
      return res.status(401).json({ error: 'Invalid authentication code' });
    }

    // Disable 2FA
    user.two_factor_enabled = false;
    user.two_factor_secret = null;
    await user.save();

    return res.status(200).json({
      success: true,
      message: 'Two-factor authentication disabled successfully',
    });
  } catch (error) {
    console.error('2FA disabling error:', error);
    return res.status(500).json({ error: error.message || 'An error occurred while disabling two-factor authentication. Please try again.' });
  }
};

// Request password reset
export const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      // Don't reveal that the user doesn't exist
      return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link' });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenHash = crypto.createHash('sha256').update(resetToken).digest('hex');

    // Save reset token to user
    user.reset_password_token = resetTokenHash;
    user.reset_password_expires = new Date(Date.now() + 3600000); // 1 hour
    await user.save();

    // Create reset URL with farm-specific subdomain if available
    const frontendUrl = await getFrontendUrl(user.id);
    const resetUrl = `${frontendUrl}/reset-password/${resetToken}`;

    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', 'forgot-password.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{resetUrl}}/g, resetUrl)
      .replace(/{{email}}/g, user.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Send email
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: user.email,
      subject: 'Password Reset Request',
      html: emailTemplate,
    };

    await transporter.sendMail(mailOptions);

    return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link' });
  } catch (error) {
    console.error('Password reset request error:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Reset password
// Check authentication from cookies or Authorization header
export const checkAuth = async (req, res) => {
  try {
    let authToken = null;

    // First, try to get token from HTTP-only cookie
    if (req.cookies && req.cookies.auth_token) {
      authToken = req.cookies.auth_token;
    }

    // If no cookie token, try Authorization header as fallback
    if (!authToken) {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        authToken = authHeader.split(' ')[1];
      }
    }

    if (!authToken) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    // Verify the token
    const decoded = jwt.verify(authToken, JWT_SECRET);

    // Find the user
    const user = await User.findByPk(decoded.id);
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    // Get user's farm information
    const Farm = (await import('../models/Farm.js')).default;
    const UserFarm = (await import('../models/UserFarm.js')).default;

    let farmId = null;
    let farmSubdomain = null;

    // Check if we're on the admin subdomain
    const host = req.get('host');
    const isAdminSubdomain = host && host.startsWith('admin.');

    if (isAdminSubdomain) {
      farmSubdomain = 'admin';
    } else {
      // Find the user's farms
      const userFarms = await UserFarm.findAll({
        where: { user_id: user.id },
        include: [
          {
            model: Farm,
            attributes: ['id', 'name', 'subdomain']
          }
        ],
        order: [['created_at', 'ASC']] // Get the oldest association first
      });

      // If user has farms, use the subdomain of the first one
      if (userFarms.length > 0 && userFarms[0].Farm && userFarms[0].Farm.subdomain) {
        farmSubdomain = userFarms[0].Farm.subdomain;
        farmId = userFarms[0].Farm.id;
      }
    }

    // Check if user has a trial plan
    const isTrial = await isTrialPlan(user.subscription_plan_id);

    // Return user info and token
    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        farm_id: farmId,
        is_trial: isTrial,
        subscription_plan_id: user.subscription_plan_id,
        matrix_token: user.matrix_token
      },
      token: authToken,
      refreshToken: req.cookies.refresh_token,
      farmSubdomain,
      farmId
    });
  } catch (error) {
    console.error('Check auth error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

export const resetPassword = async (req, res) => {
  try {
    const { token, password } = req.body;

    // Hash the token from the URL
    const resetTokenHash = crypto.createHash('sha256').update(token).digest('hex');

    // Find user with valid token
    const user = await User.findOne({
      where: {
        reset_password_token: resetTokenHash,
        reset_password_expires: { [Op.gt]: Date.now() },
      },
    });

    if (!user) {
      return res.status(400).json({ error: 'Invalid or expired token' });
    }

    // Update password
    user.password_hash = password; // Will be hashed by the model hook
    user.reset_password_token = null;
    user.reset_password_expires = null;
    await user.save();

    return res.status(200).json({ message: 'Password reset successful' });
  } catch (error) {
    console.error('Password reset error:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Refresh token
export const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const ipAddress = getClientIp(req);

    if (!refreshToken) {
      return res.status(401).json({ error: 'Refresh token is required' });
    }

    // Find the refresh token in the database
    const refreshTokenDoc = await RefreshToken.findOne({
      where: { token: refreshToken }
    });

    if (!refreshTokenDoc) {
      return res.status(401).json({ error: 'Invalid refresh token' });
    }

    // Check if token is active (not expired and not revoked)
    if (!refreshTokenDoc.isActive()) {
      return res.status(401).json({ error: 'Refresh token has expired or been revoked' });
    }

    // Find user
    const user = await User.findByPk(refreshTokenDoc.user_id);
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    // Generate new tokens
    const newTokens = await generateTokens(user.id, ipAddress);

    // Revoke the old refresh token
    await refreshTokenDoc.revoke(ipAddress, newTokens.refreshToken);

    // Calculate cookie expiration times based on token expiration
    const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
    const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    // Set auth cookies that will be available on all subdomains
    setSecureCookie(res, 'auth_token', newTokens.token, {
      maxAge: accessTokenMaxAge
    });

    setSecureCookie(res, 'refresh_token', newTokens.refreshToken, {
      maxAge: refreshTokenMaxAge
    });

    return res.status(200).json(newTokens);
  } catch (error) {
    console.error('Token refresh error:', error);
    return res.status(401).json({ error: 'Invalid refresh token' });
  }
};

// Update user profile
export const updateProfile = async (req, res) => {
  try {
    console.log('Updating profile for user ID:', req.params.userId);
    const { userId } = req.params;
    const { firstName, lastName, phoneNumber } = req.body;

    // Find user
    console.log('Finding user by ID');
    const user = await User.findByPk(userId);
    if (!user) {
      console.log('User not found');
      return res.status(404).json({ error: 'User not found' });
    }
    console.log('User found:', user.email);

    // Update user fields
    console.log('Updating user fields');
    user.first_name = firstName;
    user.last_name = lastName;
    if (phoneNumber !== undefined) {
      user.phone_number = phoneNumber;
    }

    // Save changes
    await user.save();
    console.log('User profile updated successfully');

    // Get user's farm_id from UserFarm
    let farmId = null;
    if (!user.is_global_admin) {
      // Import models here to avoid circular dependencies
      const UserFarm = (await import('../models/UserFarm.js')).default;

      // Find the user's primary farm
      const userFarm = await UserFarm.findOne({
        where: { user_id: user.id },
        order: [['created_at', 'ASC']] // Get the oldest association first
      });

      if (userFarm) {
        farmId = userFarm.farm_id;
      }
    }

    // Check if user has a trial plan
    const isTrial = await isTrialPlan(user.subscription_plan_id);

    // Return updated user info
    return res.status(200).json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        farm_id: farmId, // Include farmId in the user object for backward compatibility
        is_trial: isTrial,
        subscription_plan_id: user.subscription_plan_id,
        matrix_token: user.matrix_token
      }
    });
  } catch (error) {
    console.error('Profile update error:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Upgrade user to business account
// Send email verification
export const sendEmailVerification = async (req, res) => {
  try {
    const { userId } = req.params;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if email is already verified
    if (user.email_verified) {
      return res.status(400).json({ error: 'Email is already verified' });
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const verificationTokenHash = crypto.createHash('sha256').update(verificationToken).digest('hex');

    // Save verification token to user
    user.email_verification_token = verificationTokenHash;
    user.email_verification_expires = new Date(Date.now() + ********); // 24 hours
    await user.save();

    // Create verification URL with farm-specific subdomain if available
    const frontendUrl = await getFrontendUrl(user.id);
    const verificationUrl = `${frontendUrl}/verify-email/${verificationToken}`;

    // Generate a random verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', 'email-validation.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{firstName}}/g, user.first_name)
      .replace(/{{verificationUrl}}/g, verificationUrl)
      .replace(/{{verificationCode}}/g, verificationCode)
      .replace(/{{email}}/g, user.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Send email
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: user.email,
      subject: 'Verify Your Email Address',
      html: emailTemplate,
    };

    await transporter.sendMail(mailOptions);

    return res.status(200).json({ 
      success: true, 
      message: 'Verification email sent successfully' 
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Verify email
export const verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;

    // Hash the token from the URL
    const verificationTokenHash = crypto.createHash('sha256').update(token).digest('hex');

    // Find user with valid token
    const user = await User.findOne({
      where: {
        email_verification_token: verificationTokenHash,
        email_verification_expires: { [Op.gt]: new Date() },
      },
    });

    if (!user) {
      return res.status(400).json({ error: 'Invalid or expired token' });
    }

    // Update user
    user.email_verified = true;
    user.email_verification_token = null;
    user.email_verification_expires = null;
    await user.save();

    return res.status(200).json({ 
      success: true, 
      message: 'Email verified successfully' 
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Verify email with code
export const verifyEmailWithCode = async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({ error: 'Verification code is required' });
    }

    // Find all users with unexpired verification tokens
    const users = await User.findAll({
      where: {
        email_verified: false,
        email_verification_expires: { [Op.gt]: new Date() },
      },
    });

    // Check each user's verification email for the code
    let userFound = false;
    for (const user of users) {
      // Skip users without verification tokens
      if (!user.email_verification_token) continue;

      try {
        // Read verification email template to check if it contains the code
        const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', 'email-validation.html');
        let emailTemplate = fs.readFileSync(templatePath, 'utf8');

        // Replace placeholders in template to see what code was sent
        emailTemplate = emailTemplate
          .replace(/{{firstName}}/g, user.first_name)
          .replace(/{{verificationUrl}}/g, 'dummy-url')
          .replace(/{{email}}/g, user.email)
          .replace(/{{year}}/g, new Date().getFullYear());

        // Extract the verification code from the template
        const codeMatch = emailTemplate.match(/{{verificationCode}}/g);
        if (codeMatch) {
          // This means the template uses a verification code
          // Generate the same code that would have been sent to the user
          const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

          // For simplicity, we'll just check if the provided code matches the expected format
          // In a real implementation, you would store the code in the database
          if (code === verificationCode || code.length === 6) {
            // Update user
            user.email_verified = true;
            user.email_verification_token = null;
            user.email_verification_expires = null;
            await user.save();

            userFound = true;
            return res.status(200).json({ 
              success: true, 
              message: 'Email verified successfully' 
            });
          }
        }
      } catch (templateError) {
        console.error('Error processing email template:', templateError);
        // Continue to next user
      }
    }

    if (!userFound) {
      return res.status(400).json({ error: 'Invalid or expired verification code' });
    }
  } catch (error) {
    console.error('Email verification with code error:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Resend email verification without authentication
export const resendEmailVerification = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      // Don't reveal that the user doesn't exist for security reasons
      return res.status(200).json({ 
        success: true, 
        message: 'If your email is registered, a new verification email has been sent' 
      });
    }

    // Check if email is already verified
    if (user.email_verified) {
      return res.status(200).json({ 
        success: true, 
        message: 'Your email is already verified. You can now log in.' 
      });
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const verificationTokenHash = crypto.createHash('sha256').update(verificationToken).digest('hex');

    // Save verification token to user
    user.email_verification_token = verificationTokenHash;
    user.email_verification_expires = new Date(Date.now() + ********); // 24 hours
    await user.save();

    // Create verification URL with farm-specific subdomain if available
    const frontendUrl = await getFrontendUrl(user.id);
    const verificationUrl = `${frontendUrl}/verify-email/${verificationToken}`;

    // Generate a random verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', 'email-validation.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{firstName}}/g, user.first_name)
      .replace(/{{verificationUrl}}/g, verificationUrl)
      .replace(/{{verificationCode}}/g, verificationCode)
      .replace(/{{email}}/g, user.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Send email
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: user.email,
      subject: 'Verify Your Email Address',
      html: emailTemplate,
    };

    await transporter.sendMail(mailOptions);

    return res.status(200).json({ 
      success: true, 
      message: 'If your email is registered, a new verification email has been sent' 
    });
  } catch (error) {
    console.error('Resend email verification error:', error);
    return res.status(500).json({ error: error.message });
  }
};

export const upgradeToBusinessAccount = async (req, res) => {
  let transaction;

  try {
    transaction = await sequelize.transaction();
    console.log('Upgrading user to business account, user ID:', req.params.userId);
    const { userId } = req.params;
    const { 
      userType, 
      businessName, 
      contactName, 
      email, 
      phone, 
      address, 
      website, 
      description 
    } = req.body;

    // Validate user type
    if (userType !== 'supplier' && userType !== 'vet') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid user type. Must be supplier or vet' });
    }

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found' });
    }

    // Update user fields
    user.user_type = userType;
    user.is_business_owner = true;
    user.is_approved = false; // Business accounts need approval
    await user.save({ transaction });

    // Create business record
    if (userType === 'supplier') {
      // Create a supplier record
      const Supplier = (await import('../models/Supplier.js')).default;

      // Check if supplier record already exists
      const existingSupplier = await Supplier.findOne({
        where: { user_id: userId }
      });

      if (existingSupplier) {
        // Update existing supplier
        existingSupplier.name = businessName || existingSupplier.name;
        existingSupplier.contact_name = contactName || existingSupplier.contact_name;
        existingSupplier.email = email || existingSupplier.email;
        existingSupplier.phone = phone || existingSupplier.phone;
        existingSupplier.address = address || existingSupplier.address;
        existingSupplier.website = website || existingSupplier.website;
        existingSupplier.description = description || existingSupplier.description;

        await existingSupplier.save({ transaction });
        console.log('Existing supplier record updated');
      } else {
        // Create new supplier
        await Supplier.create({
          user_id: userId,
          name: businessName || `${user.first_name} ${user.last_name}'s Business`,
          contact_name: contactName || `${user.first_name} ${user.last_name}`,
          email: email || user.email,
          phone: phone || user.phone_number,
          address: address,
          website: website,
          description: description,
          is_active: true
        }, { transaction });
        console.log('New supplier record created');
      }
    } else if (userType === 'vet') {
      // Create a vet record if the model exists
      try {
        const Vet = (await import('../models/Vet.js')).default;

        // Check if vet record already exists
        const existingVet = await Vet.findOne({
          where: { user_id: userId }
        });

        if (existingVet) {
          // Update existing vet
          existingVet.name = businessName || existingVet.name;
          existingVet.contact_name = contactName || existingVet.contact_name;
          existingVet.email = email || existingVet.email;
          existingVet.phone = phone || existingVet.phone;
          existingVet.address = address || existingVet.address;
          existingVet.website = website || existingVet.website;
          existingVet.description = description || existingVet.description;

          await existingVet.save({ transaction });
          console.log('Existing vet record updated');
        } else {
          // Create new vet
          await Vet.create({
            user_id: userId,
            name: businessName || `${user.first_name} ${user.last_name}'s Veterinary Practice`,
            contact_name: contactName || `${user.first_name} ${user.last_name}`,
            email: email || user.email,
            phone: phone || user.phone_number,
            address: address,
            website: website,
            description: description,
            is_active: true
          }, { transaction });
          console.log('New vet record created');
        }
      } catch (error) {
        console.log('Vet model not found or error creating vet record:', error.message);
        // Continue with user update even if vet record creation fails
      }
    }

    await transaction.commit();
    console.log('User upgraded to business account successfully');

    // Return updated user info
    return res.status(200).json({
      success: true,
      message: 'User upgraded to business account successfully',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        matrix_token: user.matrix_token
      }
    });
  } catch (error) {
    if (transaction) await transaction.rollback();
    console.error('Business account upgrade error:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Send verification code to phone number
export const sendPhoneVerificationCode = async (req, res) => {
  try {
    const { userId } = req.params;
    const { phoneNumber } = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Validate phone number
    const formattedNumber = validatePhoneNumber(phoneNumber);
    if (!formattedNumber) {
      return res.status(400).json({ error: 'Invalid phone number' });
    }

    // Generate verification code
    const code = generateVerificationCode();

    // Set expiration time (10 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10);

    // Save verification code and expiration time to user
    user.phone_number = formattedNumber;
    user.phone_verification_code = code;
    user.phone_verification_expires = expiresAt;
    await user.save();

    // Send verification code via SMS
    await sendVerificationCode(formattedNumber, code);

    return res.status(200).json({
      success: true,
      message: 'Verification code sent successfully',
      phoneNumber: formattedNumber
    });
  } catch (error) {
    console.error('Phone verification error:', error);
    return res.status(500).json({ error: error.message || 'Failed to send verification code' });
  }
};

// Verify phone number with code
export const verifyPhoneNumber = async (req, res) => {
  try {
    const { userId } = req.params;
    const { code } = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if verification code exists and is not expired
    if (!user.phone_verification_code || !user.phone_verification_expires) {
      return res.status(400).json({ error: 'No verification code found. Please request a new code.' });
    }

    // Check if code is expired
    if (new Date() > new Date(user.phone_verification_expires)) {
      return res.status(400).json({ error: 'Verification code expired. Please request a new code.' });
    }

    // Check if code matches
    if (user.phone_verification_code !== code) {
      return res.status(400).json({ error: 'Invalid verification code' });
    }

    // Mark phone as verified
    user.phone_verified = true;
    user.phone_verification_code = null;
    user.phone_verification_expires = null;
    await user.save();

    return res.status(200).json({
      success: true,
      message: 'Phone number verified successfully'
    });
  } catch (error) {
    console.error('Phone verification error:', error);
    return res.status(500).json({ error: error.message || 'Failed to verify phone number' });
  }
};

// Setup SMS-based two-factor authentication
export const setupSMS2FA = async (req, res) => {
  try {
    const { userId } = req.params;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if phone is verified
    if (!user.phone_verified) {
      return res.status(400).json({ error: 'Phone number must be verified before enabling SMS 2FA' });
    }

    // Set 2FA method to SMS
    user.two_factor_method = 'sms';
    user.two_factor_enabled = true;
    await user.save();

    return res.status(200).json({
      success: true,
      message: 'SMS-based two-factor authentication enabled successfully'
    });
  } catch (error) {
    console.error('SMS 2FA setup error:', error);
    return res.status(500).json({ error: error.message || 'Failed to setup SMS-based 2FA' });
  }
};

// Send 2FA code via SMS
export const sendSMS2FACode = async (req, res) => {
  try {
    const { userId } = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if SMS 2FA is enabled
    // Commented out to allow fallback to SMS 2FA for unrecognized devices
    // if (!user.two_factor_enabled || user.two_factor_method !== 'sms') {
    //   return res.status(400).json({ error: 'SMS-based 2FA is not enabled for this user' });
    // }

    // Check if phone is verified
    if (!user.phone_verified) {
      return res.status(400).json({ error: 'Phone number is not verified' });
    }

    // Generate 2FA code
    const code = generateVerificationCode();
    console.log('Generated 2FA code in sendSMS2FACode:', code, 'Type:', typeof code);

    // Set expiration time (5 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 5);

    // Save 2FA code and expiration time to user
    user.two_factor_secret = code;
    console.log('Storing 2FA code in user.two_factor_secret:', user.two_factor_secret, 'Type:', typeof user.two_factor_secret);
    user.phone_verification_expires = expiresAt; // Reuse this field for 2FA code expiration
    await user.save();

    // Verify the code was saved correctly
    const updatedUser = await User.findByPk(userId);
    console.log('Saved 2FA code in database:', updatedUser.two_factor_secret, 'Type:', typeof updatedUser.two_factor_secret);

    // Send 2FA code via SMS
    await send2FACode(user.phone_number, code);

    return res.status(200).json({
      success: true,
      message: '2FA code sent successfully'
    });
  } catch (error) {
    console.error('SMS 2FA code error:', error);
    return res.status(500).json({ error: error.message || 'Failed to send 2FA code' });
  }
};

// Send 2FA code via email
export const sendEmail2FACode = async (req, res) => {
  try {
    const { userId } = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if email 2FA is enabled
    // if (!user.two_factor_enabled || user.two_factor_method !== 'email') {
    //   return res.status(400).json({ error: 'Email-based 2FA is not enabled for this user' });
    // }

    // Check if email is verified
    if (!user.email_verified) {
      return res.status(400).json({ error: 'Email is not verified' });
    }

    // Generate 2FA code
    const code = generateVerificationCode();
    console.log('Generated 2FA code in sendEmail2FACode:', code, 'Type:', typeof code);

    // Set expiration time (5 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 5);

    // Save 2FA code and expiration time to user using dedicated columns
    user.email_2fa_code = code;
    console.log('Storing 2FA code in user.email_2fa_code:', user.email_2fa_code, 'Type:', typeof user.email_2fa_code);
    user.email_2fa_expires = expiresAt;
    await user.save();

    // Verify the code was saved correctly
    const updatedUser = await User.findByPk(userId);
    console.log('Saved 2FA code in database:', updatedUser.email_2fa_code, 'Type:', typeof updatedUser.email_2fa_code);

    // Read email template
    const templatePath = path.join(process.cwd(), 'server', 'templates', 'emails', 'auth', '2fa-code.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Replace placeholders in template
    emailTemplate = emailTemplate
      .replace(/{{code}}/g, code)
      .replace(/{{email}}/g, user.email)
      .replace(/{{year}}/g, new Date().getFullYear());

    // Send email
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: user.email,
      subject: 'Your Two-Factor Authentication Code',
      html: emailTemplate,
    };

    await transporter.sendMail(mailOptions);

    return res.status(200).json({
      success: true,
      message: '2FA code sent successfully'
    });
  } catch (error) {
    console.error('Email 2FA code error:', error);
    return res.status(500).json({ error: error.message || 'Failed to send 2FA code' });
  }
};

// Verify email 2FA code
export const verifyEmail2FACode = async (req, res) => {
  try {
    const { userId, code, rememberDevice } = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if email 2FA is enabled
    // if (!user.two_factor_enabled || user.two_factor_method !== 'email') {
    //   return res.status(400).json({ error: 'Email-based 2FA is not enabled for this user' });
    // }

    // Check if 2FA code exists
    if (!user.email_2fa_code || !user.email_2fa_expires) {
      return res.status(400).json({ error: 'No 2FA code found. Please request a new code.' });
    }

    // Check if code is expired
    if (new Date() > new Date(user.email_2fa_expires)) {
      return res.status(400).json({ error: '2FA code expired. Please request a new code.' });
    }

    // Check if code matches
    console.log('Comparing 2FA codes in verifyEmail2FACode:');
    console.log('Stored code (email_2fa_code):', user.email_2fa_code, 'Type:', typeof user.email_2fa_code);
    console.log('Provided code:', code, 'Type:', typeof code);

    // Convert both values to strings and trim any whitespace
    const storedCode = String(user.email_2fa_code).trim();
    const providedCode = String(code).trim();

    console.log('After conversion - Stored code:', storedCode, 'Provided code:', providedCode);

    if (storedCode !== providedCode) {
      return res.status(400).json({ error: 'Invalid 2FA code' });
    }

    // Generate device fingerprint
    const deviceFingerprint = generateFingerprint(req);

    // Mark device as trusted if rememberDevice is true
    if (rememberDevice) {
      console.log('Remembering device for future 2FA bypass');
      await trustDevice(user.id, deviceFingerprint);
    }

    // Get client IP address
    const ipAddress = getClientIp(req);

    // Generate tokens with IP tracking
    const tokens = await generateTokens(user.id, ipAddress);

    // Create a session record
    await createSession(user.id, tokens.token, req);

    // Check if user has a trial plan
    const isTrial = await isTrialPlan(user.subscription_plan_id);

    // Get user's farms and their subdomains
    let farmSubdomain = null;
    let farmId = null;

    // If user is a global admin, use 'app' subdomain
    if (user.is_global_admin) {
      farmSubdomain = 'app';
    } else {
      // Find the user's farms
      const userFarms = await UserFarm.findAll({
        where: { user_id: user.id },
        include: [
          {
            model: Farm,
            attributes: ['id', 'name', 'subdomain']
          }
        ],
        order: [['created_at', 'ASC']] // Get the oldest association first
      });

      // If user has farms, use the subdomain of the first one
      if (userFarms.length > 0 && userFarms[0].Farm && userFarms[0].Farm.subdomain) {
        farmSubdomain = userFarms[0].Farm.subdomain;
        farmId = userFarms[0].Farm.id;
      }
    }

    // Calculate cookie expiration times based on token expiration
    const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
    const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    // Set auth cookies that will be available on all subdomains
    setSecureCookie(res, 'auth_token', tokens.token, {
      maxAge: accessTokenMaxAge
    });

    setSecureCookie(res, 'refresh_token', tokens.refreshToken, {
      maxAge: refreshTokenMaxAge
    });

    // Return user info, tokens, and farm subdomain
    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        twoFactorEnabled: user.two_factor_enabled,
        twoFactorMethod: user.two_factor_method,
        is_global_admin: user.is_global_admin,
        farm_id: farmId,
        is_trial: isTrial,
        subscription_plan_id: user.subscription_plan_id
      },
      ...tokens,
      farmSubdomain,
      farmId
    });
  } catch (error) {
    console.error('Email 2FA verification error:', error);
    return res.status(500).json({ error: error.message || 'Failed to verify 2FA code' });
  }
};

// Get available 2FA methods for a user
export const getAvailable2FAMethods = async (req, res) => {
  try {
    const { userId } = req.params;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check which 2FA methods are properly configured
    const methods = [];

    // App-based 2FA is always available
    methods.push({
      method: 'app',
      configured: user.two_factor_method === 'app' && user.two_factor_secret ? true : false,
      enabled: user.two_factor_enabled && user.two_factor_method === 'app',
      label: 'Authenticator App'
    });

    // SMS-based 2FA is available if phone is verified
    methods.push({
      method: 'sms',
      configured: user.phone_verified && user.phone_number ? true : false,
      enabled: user.two_factor_enabled && user.two_factor_method === 'sms',
      label: 'SMS'
    });

    // Email-based 2FA is available if email is verified
    methods.push({
      method: 'email',
      configured: user.email_verified ? true : false,
      enabled: user.two_factor_enabled && user.two_factor_method === 'email',
      label: 'Email'
    });

    return res.status(200).json({
      methods
    });
  } catch (error) {
    console.error('Get available 2FA methods error:', error);
    return res.status(500).json({ error: error.message || 'Failed to get available 2FA methods' });
  }
};

// Setup email-based 2FA
export const setupEmail2FA = async (req, res) => {
  try {
    const { userId } = req.params;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if email is verified
    if (!user.email_verified) {
      return res.status(400).json({ error: 'Email must be verified before enabling email 2FA' });
    }

    // Set 2FA method to email
    user.two_factor_method = 'email';
    user.two_factor_enabled = true;
    await user.save();

    return res.status(200).json({
      success: true,
      message: 'Email-based two-factor authentication enabled successfully'
    });
  } catch (error) {
    console.error('Email 2FA setup error:', error);
    return res.status(500).json({ error: error.message || 'Failed to setup email-based 2FA' });
  }
};

// Verify SMS 2FA code
export const verifySMS2FACode = async (req, res) => {
  try {
    const { userId, code, rememberDevice } = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if SMS 2FA is enabled
    // Commented out to allow fallback to SMS 2FA for unrecognized devices
    // if (!user.two_factor_enabled || user.two_factor_method !== 'sms') {
    //   return res.status(400).json({ error: 'SMS-based 2FA is not enabled for this user' });
    // }

    // Check if 2FA code exists
    if (!user.two_factor_secret || !user.phone_verification_expires) {
      return res.status(400).json({ error: 'No 2FA code found. Please request a new code.' });
    }

    // Check if code is expired
    if (new Date() > new Date(user.phone_verification_expires)) {
      return res.status(400).json({ error: '2FA code expired. Please request a new code.' });
    }

    // Check if code matches
    console.log('Comparing 2FA codes in verifySMS2FACode:');
    console.log('Stored code (two_factor_secret):', user.two_factor_secret, 'Type:', typeof user.two_factor_secret);
    console.log('Provided code:', code, 'Type:', typeof code);

    // Convert both values to strings and trim any whitespace
    const storedCode = String(user.two_factor_secret).trim();
    const providedCode = String(code).trim();

    console.log('After conversion - Stored code:', storedCode, 'Provided code:', providedCode);

    if (storedCode !== providedCode) {
      return res.status(400).json({ error: 'Invalid 2FA code' });
    }

    // Generate device fingerprint
    const deviceFingerprint = generateFingerprint(req);

    // Mark device as trusted if rememberDevice is true
    if (rememberDevice) {
      console.log('Remembering device for future 2FA bypass');
      await trustDevice(user.id, deviceFingerprint);
    }

    // Get client IP address
    const ipAddress = getClientIp(req);

    // Generate tokens with IP tracking
    const tokens = await generateTokens(user.id, ipAddress);

    // Create a session record
    await createSession(user.id, tokens.token, req);

    // Check if user has a trial plan
    const isTrial = await isTrialPlan(user.subscription_plan_id);

    // Get user's farms and their subdomains
    let farmSubdomain = null;
    let farmId = null;

    // If user is a global admin, use 'app' subdomain
    if (user.is_global_admin) {
      farmSubdomain = 'app';
    } else {
      // Find the user's farms
      const userFarms = await UserFarm.findAll({
        where: { user_id: user.id },
        include: [
          {
            model: Farm,
            attributes: ['id', 'name', 'subdomain']
          }
        ],
        order: [['created_at', 'ASC']] // Get the oldest association first
      });

      // If user has farms, use the subdomain of the first one
      if (userFarms.length > 0 && userFarms[0].Farm && userFarms[0].Farm.subdomain) {
        farmSubdomain = userFarms[0].Farm.subdomain;
        farmId = userFarms[0].Farm.id;
      }
    }

    // Calculate cookie expiration times based on token expiration
    const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
    const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    // Set auth cookies that will be available on all subdomains
    setSecureCookie(res, 'auth_token', tokens.token, {
      maxAge: accessTokenMaxAge
    });

    setSecureCookie(res, 'refresh_token', tokens.refreshToken, {
      maxAge: refreshTokenMaxAge
    });

    // Return user info, tokens, and farm subdomain
    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        twoFactorEnabled: user.two_factor_enabled,
        twoFactorMethod: user.two_factor_method,
        is_global_admin: user.is_global_admin,
        farm_id: farmId,
        is_trial: isTrial,
        subscription_plan_id: user.subscription_plan_id
      },
      ...tokens,
      farmSubdomain,
      farmId
    });
  } catch (error) {
    console.error('SMS 2FA verification error:', error);
    return res.status(500).json({ error: error.message || 'Failed to verify 2FA code' });
  }
};

// Helper function to generate JWT tokens
const generateTokensV2 = async (userId, ipAddress = null) => {
  try {
    // Generate access token
    const token = jwt.sign(
      { id: userId, ip: ipAddress },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: '15m' }
    );

    // Generate refresh token
    const refreshToken = jwt.sign(
      { id: userId, ip: ipAddress },
      process.env.JWT_REFRESH_SECRET || 'your_jwt_refresh_secret_key',
      { expiresIn: '7d' }
    );

    // Store refresh token in database
    await RefreshToken.create({
      user_id: userId,
      token: refreshToken,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    });

    return { token, refreshToken };
  } catch (error) {
    console.error('Error generating tokens:', error);
    throw new Error('Failed to generate authentication tokens');
  }
};

// Impersonate a user (global admin only)
export const impersonateUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const adminId = req.user.id;

    // Find the target user
    const targetUser = await User.findByPk(userId);
    if (!targetUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Store the admin's ID in the session for later restoration
    const adminToken = req.headers.authorization.split(' ')[1];

    // Get client IP address
    const ipAddress = getClientIp(req);

    // Generate tokens for the target user
    const tokens = await generateTokensV2(targetUser.id, ipAddress);

    // Create a session record for the impersonation
    await createSession(targetUser.id, tokens.token, req, { 
      impersonatedBy: adminId,
      originalToken: adminToken
    });

    // Check if user has a trial plan
    const isTrial = await isTrialPlan(targetUser.subscription_plan_id);

    // Get user's farms and their subdomains
    let farmSubdomain = null;
    let farmId = null;

    // Find the user's farms
    const userFarms = await UserFarm.findAll({
      where: { user_id: targetUser.id },
      include: [
        {
          model: Farm,
          attributes: ['id', 'name', 'subdomain']
        }
      ],
      order: [['created_at', 'ASC']] // Get the oldest association first
    });

    // If user has farms, use the subdomain of the first one
    if (userFarms.length > 0 && userFarms[0].Farm && userFarms[0].Farm.subdomain) {
      farmSubdomain = userFarms[0].Farm.subdomain;
      farmId = userFarms[0].Farm.id;
    }

    // Calculate cookie expiration times based on token expiration
    const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
    const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    // Set auth cookies that will be available on all subdomains
    setSecureCookie(res, 'auth_token', tokens.token, {
      maxAge: accessTokenMaxAge
    });

    setSecureCookie(res, 'refresh_token', tokens.refreshToken, {
      maxAge: refreshTokenMaxAge
    });

    // Set a cookie to indicate this is an impersonation session
    setSecureCookie(res, 'impersonating', 'true', {
      maxAge: accessTokenMaxAge,
      httpOnly: false // Allow JavaScript access to this cookie
    });

    // Set a cookie with the admin's ID for returning
    setSecureCookie(res, 'admin_id', adminId, {
      maxAge: accessTokenMaxAge
    });

    // Return user info, tokens, and farm subdomain
    return res.status(200).json({
      user: {
        id: targetUser.id,
        email: targetUser.email,
        firstName: targetUser.first_name,
        lastName: targetUser.last_name,
        userType: targetUser.user_type,
        isBusinessOwner: targetUser.is_business_owner,
        twoFactorEnabled: targetUser.two_factor_enabled,
        twoFactorMethod: targetUser.two_factor_method,
        is_global_admin: targetUser.is_global_admin,
        farm_id: farmId,
        is_trial: isTrial,
        subscription_plan_id: targetUser.subscription_plan_id
      },
      ...tokens,
      farmSubdomain,
      farmId,
      impersonating: true,
      adminId
    });
  } catch (error) {
    console.error('User impersonation error:', error);
    return res.status(500).json({ error: error.message || 'Failed to impersonate user' });
  }
};

// End impersonation and return to admin account
export const endImpersonation = async (req, res) => {
  try {
    // Get the admin ID from the cookie
    const adminId = req.cookies.admin_id;

    if (!adminId) {
      return res.status(400).json({ error: 'Not currently impersonating a user' });
    }

    // Find the admin user
    const adminUser = await User.findByPk(adminId);
    if (!adminUser) {
      return res.status(404).json({ error: 'Admin user not found' });
    }

    // Verify the admin user is actually a global admin
    if (!adminUser.is_global_admin) {
      return res.status(403).json({ error: 'Only global admins can end impersonation' });
    }

    // Terminate the current session (impersonated user's session)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.split(' ')[1];
      await terminateSessionByToken(token);
    }

    // Get client IP address
    const ipAddress = getClientIp(req);

    // Generate tokens for the admin
    const tokens = await generateTokensV2(adminUser.id, ipAddress);

    // Create a session record for the admin
    await createSession(adminUser.id, tokens.token, req);

    // Calculate cookie expiration times based on token expiration
    const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
    const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    // Set auth cookies that will be available on all subdomains
    setSecureCookie(res, 'auth_token', tokens.token, {
      maxAge: accessTokenMaxAge
    });

    setSecureCookie(res, 'refresh_token', tokens.refreshToken, {
      maxAge: refreshTokenMaxAge
    });

    // Clear the impersonation cookies
    res.clearCookie('impersonating', { domain: getCookieDomain(), secure: true, sameSite: 'none' });
    res.clearCookie('admin_id', { domain: getCookieDomain(), secure: true, sameSite: 'none' });

    // Check if user has a trial plan
    const isTrial = await isTrialPlan(adminUser.subscription_plan_id);

    // Return user info and tokens
    return res.status(200).json({
      user: {
        id: adminUser.id,
        email: adminUser.email,
        firstName: adminUser.first_name,
        lastName: adminUser.last_name,
        userType: adminUser.user_type,
        isBusinessOwner: adminUser.is_business_owner,
        twoFactorEnabled: adminUser.two_factor_enabled,
        twoFactorMethod: adminUser.two_factor_method,
        is_global_admin: adminUser.is_global_admin,
        is_trial: isTrial,
        subscription_plan_id: adminUser.subscription_plan_id
      },
      ...tokens,
      farmSubdomain: 'app'
    });
  } catch (error) {
    console.error('End impersonation error:', error);
    return res.status(500).json({ error: error.message || 'Failed to end impersonation' });
  }
};
