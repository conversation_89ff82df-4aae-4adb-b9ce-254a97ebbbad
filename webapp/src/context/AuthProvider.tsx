import React, { useState, useEffect, useCallback, useRef, useContext } from 'react';
import axios from 'axios';
import { AuthContext, AuthContextType } from './AuthContext';
import SessionTimeoutDialog from '../components/SessionTimeoutDialog';
import { API_URL } from '../config';
import { useNavigate } from 'react-router-dom';
import type { User } from '../types/user';
import { getStorageItem, setStorageItem, removeStorageItem, getStorageJSON, setStorageJSON } from '../utils/storageUtils';
import Cookies from 'js-cookie';
import { FarmContext } from './FarmContext';

// Configure axios to include credentials in all requests
axios.defaults.withCredentials = true;

// Session timeout constants
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes in milliseconds
const WARNING_BEFORE_TIMEOUT = 60 * 1000; // Show warning 1 minute before timeout
const LAST_ACTIVITY_KEY = 'lastActivity'; // Key for storing last activity timestamp

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isImpersonating, setIsImpersonating] = useState<boolean>(false);
  const [adminId, setAdminId] = useState<string | null>(null);
  const { clearCurrentFarm } = useContext(FarmContext);

  // Session timeout state
  const [showTimeoutWarning, setShowTimeoutWarning] = useState<boolean>(false);
  const [remainingTime, setRemainingTime] = useState<number>(60); // 60 seconds warning

  const sessionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Logout
  const logout = useCallback(async () => {
    try {
      // Call the server logout endpoint to clear cookies
      await axios.post(`${API_URL}/auth/logout`, {}, { withCredentials: true });
    } catch (error) {
      // Continue with client-side logout even if server request fails
      // No need to log the error to console
    } finally {
      // Clear state
      setUser(null);
      setToken(null);

      // Clear localStorage data (non-sensitive data only)
      // HTTP-only cookies are cleared by the server
      removeStorageItem('user');
      removeStorageItem('farmSubdomain');
      removeStorageItem('farmId');
      removeStorageItem(LAST_ACTIVITY_KEY);

      console.log('✅ Logout successful - HTTP-only cookies cleared by server');

      // Clear farm session data
      clearCurrentFarm();

      // Clear Authorization header
      delete axios.defaults.headers.common['Authorization'];

      // Clear timeouts using refs
      if (sessionTimeoutRef.current) clearTimeout(sessionTimeoutRef.current);
      if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);

      sessionTimeoutRef.current = null;
      warningTimeoutRef.current = null;

      // Hide warning dialog
      setShowTimeoutWarning(false);

      // Redirect to login page
      navigate('/login');
    }
  }, [navigate, clearCurrentFarm]);

  // Reset session timeout
  const resetSessionTimeout = useCallback(() => {
    // Clear existing timeouts using refs
    if (sessionTimeoutRef.current) clearTimeout(sessionTimeoutRef.current);
    if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);

    // Only set timeouts if user is logged in
    if (user && token) {
      // Update last activity timestamp
      const now = Date.now();
      setStorageItem(LAST_ACTIVITY_KEY, now.toString());

      // Set timeout for showing warning
      const warningTimer = setTimeout(() => {
        setShowTimeoutWarning(true);

        // Start countdown timer
        const countdownInterval = setInterval(() => {
          setRemainingTime(prev => {
            if (prev <= 1) {
              clearInterval(countdownInterval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);

        // Clear interval when session ends
        setTimeout(() => clearInterval(countdownInterval), WARNING_BEFORE_TIMEOUT);

      }, SESSION_TIMEOUT - WARNING_BEFORE_TIMEOUT);

      // Set timeout for logging out
      const sessionTimer = setTimeout(() => {
        logout();
      }, SESSION_TIMEOUT);

      // Store timeouts in refs instead of state
      warningTimeoutRef.current = warningTimer;
      sessionTimeoutRef.current = sessionTimer;
    }
  }, [user, token, logout]);

  // Extend session
  const extendSession = useCallback(() => {
    setShowTimeoutWarning(false);
    setRemainingTime(60); // Reset remaining time to initial value
    resetSessionTimeout();
  }, [resetSessionTimeout]);

  // Track user activity
  useEffect(() => {
    if (!user) return;

    const handleActivity = () => {
      if (showTimeoutWarning) {
        return;
      }
      resetSessionTimeout();
    };

    window.addEventListener('mousemove', handleActivity);
    window.addEventListener('keydown', handleActivity);
    window.addEventListener('click', handleActivity);
    window.addEventListener('scroll', handleActivity);

    return () => {
      window.removeEventListener('mousemove', handleActivity);
      window.removeEventListener('keydown', handleActivity);
      window.removeEventListener('click', handleActivity);
      window.removeEventListener('scroll', handleActivity);
    };
  }, [user, resetSessionTimeout, showTimeoutWarning]);

  // Initialize session timeout
  useEffect(() => {
    if (user && token) {
      resetSessionTimeout();
    }
  }, [user, token, resetSessionTimeout]);

  // Initialize auth state from HTTP-only cookies (primary) with localStorage fallback
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check for impersonation cookies
        const isImpersonatingCookie = Cookies.get('impersonating');
        const adminIdCookie = Cookies.get('admin_id');

        // ALWAYS check HTTP-only cookies first via server validation
        console.log('Checking authentication via HTTP-only cookies...');
        const response = await axios.get(`${API_URL}/auth/check-auth`, { withCredentials: true });

        if (response.data && response.data.user) {
          console.log('✅ Authentication successful via HTTP-only cookies');

          // Store user and token in state
          setUser(response.data.user);
          setToken(response.data.token);

          // Store user data in localStorage for quick access (non-sensitive data only)
          setStorageJSON('user', response.data.user);

          // Store farm information if available
          if (response.data.farmSubdomain) {
            setStorageItem('farmSubdomain', response.data.farmSubdomain);
          }
          if (response.data.farmId) {
            setStorageItem('farmId', response.data.farmId);
          }

          // Set impersonation state if cookies exist
          if (isImpersonatingCookie === 'true' && adminIdCookie) {
            setIsImpersonating(true);
            setAdminId(adminIdCookie);
          }

          // Set default Authorization header for all requests (for immediate use)
          axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

          // Update last activity timestamp
          const now = Date.now();
          setStorageItem(LAST_ACTIVITY_KEY, now.toString());
        } else {
          console.log('❌ No valid HTTP-only cookies found');
          throw new Error('No valid authentication cookies');
        }
      } catch (error) {
        console.log('🔄 HTTP-only cookie check failed, trying localStorage fallback...');

        // Fallback to localStorage if cookies fail
        const storedUser = getStorageJSON<User>('user');
        const storedToken = getStorageItem('token');
        const lastActivityStr = getStorageItem(LAST_ACTIVITY_KEY);

        // Check if session has expired due to inactivity
        const checkSessionExpiration = () => {
          if (lastActivityStr) {
            const lastActivity = parseInt(lastActivityStr, 10);
            const now = Date.now();
            const timeSinceLastActivity = now - lastActivity;

            // If time since last activity exceeds session timeout, log out
            if (timeSinceLastActivity > SESSION_TIMEOUT) {
              console.log('⏰ Session expired due to inactivity');
              // Clear storage
              removeStorageItem('user');
              removeStorageItem('token');
              removeStorageItem('refreshToken');
              removeStorageItem('farmSubdomain');
              removeStorageItem('farmId');
              removeStorageItem(LAST_ACTIVITY_KEY);

              // Clear state
              setUser(null);
              setToken(null);

              // Clear Authorization header
              delete axios.defaults.headers.common['Authorization'];

              return false;
            }
          }
          return true;
        };

        if (storedUser && storedToken && checkSessionExpiration()) {
          console.log('✅ Authentication successful via localStorage fallback');

          setUser(storedUser);
          setToken(storedToken);

          // Check for impersonation cookies
          const isImpersonatingCookie = Cookies.get('impersonating');
          const adminIdCookie = Cookies.get('admin_id');

          // Set impersonation state if cookies exist
          if (isImpersonatingCookie === 'true' && adminIdCookie) {
            setIsImpersonating(true);
            setAdminId(adminIdCookie);
          }

          // Set default Authorization header for all requests
          axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;

          // Update last activity timestamp
          const now = Date.now();
          setStorageItem(LAST_ACTIVITY_KEY, now.toString());
        } else {
          console.log('❌ No valid authentication found in localStorage either');

          // Clear any stale data
          removeStorageItem('user');
          removeStorageItem('token');
          removeStorageItem('refreshToken');
          removeStorageItem('farmSubdomain');
          removeStorageItem('farmId');
          removeStorageItem(LAST_ACTIVITY_KEY);

          // Clear state
          setUser(null);
          setToken(null);

          // Clear Authorization header
          delete axios.defaults.headers.common['Authorization'];
        }
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Set up axios interceptor to handle unauthorized responses
  useEffect(() => {
    // Create a response interceptor
    const interceptor = axios.interceptors.response.use(
      (response) => response, // Return successful responses as-is
      (error) => {
        // Handle unauthorized or forbidden responses
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          // Check if the request URL is for any auth-related endpoint
          const url = error.config.url;

          // API endpoints for auth routes
          const isAuthApiEndpoint = url && (
            url.includes('/auth/register') ||
            url.includes('/auth/register-business') ||
            url.includes('/auth/forgot-password') ||
            url.includes('/auth/reset-password') ||
            url.includes('/auth/verify-email') ||
            url.includes('/auth/verify-2fa') ||
            url.includes('/auth/login') ||
            url.includes('/auth/verify-email-2fa') ||
            url.includes('/auth/verify-sms-2fa') ||
            url.includes('/auth/logout') ||
            url.includes('/auth/check-auth') ||
            url.includes('/auth/send-phone-verification') ||
            url.includes('/auth/verify-phone') ||
            url.includes('/auth/setup-2fa') ||
            url.includes('/auth/setup-sms-2fa') ||
            url.includes('/auth/confirm-2fa') ||
            url.includes('/auth/disable-2fa') ||
            url.includes('/auth/impersonate') ||
            url.includes('/auth/end-impersonation') ||
            url.includes('/sign/') // For public document signing
          );

          // Only logout if not an auth exempt route
          if (!isAuthApiEndpoint) {
            // Call logout to clear user session data without logging to console
            logout();
          }
        }
        return Promise.reject(error);
      }
    );

    // Clean up the interceptor when the component unmounts
    return () => {
      axios.interceptors.response.eject(interceptor);
    };
  }, [logout]);

  // Ensure global admin status is properly set
  useEffect(() => {
    // If the user is logged in but doesn't have the is_global_admin property
    if (user && token) {
      // Only update if the property is undefined (not if it's false)
      if (user.is_global_admin === undefined) {
        // Fetch the user's global admin status from the server
        const fetchGlobalAdminStatus = async () => {
          try {
            const response = await axios.get(`${API_URL}/users/${user.id}/details`, {
              headers: {
                Authorization: `Bearer ${token}`
              },
              withCredentials: true
            });

            // Get the global admin status from the server response
            const isGlobalAdmin = response.data.user?.is_global_admin || false;

            // Update the user object with the is_global_admin property
            const updatedUser = { ...user, is_global_admin: isGlobalAdmin };
            setUser(updatedUser);
            setStorageJSON('user', updatedUser);
          } catch (error) {
            console.error('Error fetching global admin status:', error);
            // If there's an error, assume the user is not a global admin
            const updatedUser = { ...user, is_global_admin: false };
            setUser(updatedUser);
            setStorageJSON('user', updatedUser);
          }
        };

        fetchGlobalAdminStatus();
      }
    }
  }, [user, token, API_URL]);

  // Update user data in context
  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      setStorageJSON('user', updatedUser);
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Login
  const login = async (email: string, password: string, farmSubdomain?: string | null) => {
    try {
      setLoading(true);
      clearError();

      // Include farmSubdomain in the request if provided
      const response = await axios.post(`${API_URL}/auth/login`, { 
        email, 
        password,
        farmSubdomain 
      }, { withCredentials: true });

      // Check if 2FA is required
      if (response.data.requireTwoFactor) {
        return {
          requireTwoFactor: true,
          userId: response.data.userId,
          farmSubdomain: response.data.farmSubdomain,
          farmId: response.data.farmId
        };
      }

      // Store user and token
      setUser(response.data.user);
      setToken(response.data.token);

      // Store user data in localStorage for quick access (non-sensitive data only)
      // Tokens are stored as HTTP-only cookies by the server
      setStorageJSON('user', response.data.user);

      console.log('✅ Login successful - tokens stored as HTTP-only cookies');

      // Store farm information if available
      if (response.data.farmSubdomain) {
        setStorageItem('farmSubdomain', response.data.farmSubdomain);
      }
      if (response.data.farmId) {
        setStorageItem('farmId', response.data.farmId);
      }

      // Set default Authorization header for all requests
      axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

      return {
        farmSubdomain: response.data.farmSubdomain,
        farmId: response.data.farmId
      };
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.error || 'Login failed');
      } else {
        setError('Login failed');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add the rest of your auth methods here (register, verifyTwoFactor, etc.)
  // Register a new user
  const register = async (email: string, password: string, firstName: string, lastName: string, phoneNumber?: string, userType?: string, isBusinessOwner?: boolean, farmName?: string, subdomain?: string) => {
    try {
      setLoading(true);
      clearError();

      const response = await axios.post(`${API_URL}/auth/register`, {
        email,
        password,
        firstName,
        lastName,
        phoneNumber,
        userType,
        isBusinessOwner,
        farmName,
        subdomain
      }, { withCredentials: true });

      // Store user and token
      setUser(response.data.user);
      setToken(response.data.token);

      // Store user data in localStorage for quick access (non-sensitive data only)
      // Tokens are stored as HTTP-only cookies by the server
      setStorageJSON('user', response.data.user);

      console.log('✅ Registration successful - tokens stored as HTTP-only cookies');

      // Store farm information if available
      if (response.data.farmSubdomain) {
        setStorageItem('farmSubdomain', response.data.farmSubdomain);
      }
      if (response.data.farmId) {
        setStorageItem('farmId', response.data.farmId);
      }

      // Set default Authorization header for all requests
      axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

      return response.data;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.error || 'Registration failed');
      } else {
        setError('Registration failed');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Send phone verification code
  const sendPhoneVerificationCode = async (phoneNumber: string) => {
    try {
      setLoading(true);
      clearError();

      if (!user || !user.id) {
        throw new Error('User not authenticated');
      }

      await axios.post(`${API_URL}/auth/send-phone-verification/${user.id}`, { 
        phoneNumber 
      }, { withCredentials: true });

      return;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.error || 'Failed to send verification code');
      } else {
        setError('Failed to send verification code');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Verify phone number with code
  const verifyPhoneNumber = async (code: string) => {
    try {
      setLoading(true);
      clearError();

      if (!user || !user.id) {
        throw new Error('User not authenticated');
      }

      const response = await axios.post(`${API_URL}/auth/verify-phone/${user.id}`, { 
        code 
      }, { withCredentials: true });

      if (response.data.success) {
        // Update user with verified phone
        updateUser({ phoneVerified: true });
      }

      return;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.error || 'Failed to verify phone number');
      } else {
        setError('Failed to verify phone number');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get token function
  const getToken = useCallback(async (): Promise<string | null> => {
    return token;
  }, [token]);

  // Context value
  const contextValue: AuthContextType = {
    user,
    token,
    loading,
    error,
    login,
    register,
    logout,
    sendPhoneVerificationCode,
    verifyPhoneNumber,
    getToken,
    verifyTwoFactor: async (userId: string, token: string, farmSubdomain?: string | null, farmId?: string | null, method?: string | null, rememberDevice?: boolean) => {
      try {
        setLoading(true);
        clearError();

        // Use the appropriate endpoint based on the 2FA method
        let endpoint = `${API_URL}/auth/verify-2fa`;
        if (method === 'email') {
          endpoint = `${API_URL}/auth/verify-email-2fa`;
        } else if (method === 'sms') {
          endpoint = `${API_URL}/auth/verify-sms-2fa`;
        }

        const response = await axios.post(endpoint, { 
          userId, 
          code: token, // Use 'code' instead of 'token' for email and SMS 2FA
          rememberDevice: rememberDevice ?? false, // Use the rememberDevice parameter or default to false
          farmSubdomain,
          farmId,
          method
        }, { withCredentials: true });

        // Store user and token
        setUser(response.data.user);
        setToken(response.data.token);

        // Store user data in localStorage for quick access (non-sensitive data only)
        // Tokens are stored as HTTP-only cookies by the server
        setStorageJSON('user', response.data.user);

        console.log('✅ 2FA verification successful - tokens stored as HTTP-only cookies');

        // Store farm information if available
        if (response.data.farmSubdomain) {
          setStorageItem('farmSubdomain', response.data.farmSubdomain);
        }
        if (response.data.farmId) {
          setStorageItem('farmId', response.data.farmId);
        }

        // Set default Authorization header for all requests
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

        // Return farm information for redirection
        return {
          farmSubdomain: response.data.farmSubdomain,
          farmId: response.data.farmId
        };
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || '2FA verification failed');
        } else {
          setError('2FA verification failed');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    setupTwoFactor: async () => {
      try {
        setLoading(true);
        clearError();

        if (!user || !user.id) {
          throw new Error('User not authenticated');
        }

        const response = await axios.get(`${API_URL}/auth/setup-2fa/${user.id}`, { withCredentials: true });

        return {
          secret: response.data.secret,
          qrCode: response.data.qrCode
        };
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Failed to setup two-factor authentication');
        } else {
          setError('Failed to setup two-factor authentication');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    setupSMS2FA: async () => {
      try {
        setLoading(true);
        clearError();

        if (!user || !user.id) {
          throw new Error('User not authenticated');
        }

        // Check if phone is verified
        if (!user.phoneVerified) {
          throw new Error('Phone number must be verified before enabling SMS 2FA');
        }

        const response = await axios.post(`${API_URL}/auth/setup-sms-2fa/${user.id}`, {}, { withCredentials: true });

        if (response.data.success) {
          // Update user with SMS 2FA enabled
          updateUser({ 
            twoFactorEnabled: true,
            twoFactorMethod: 'sms'
          });
        }

        return response.data;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Failed to setup SMS-based 2FA');
        } else {
          setError('Failed to setup SMS-based 2FA');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    confirmTwoFactor: async (token: string) => {
      try {
        setLoading(true);
        clearError();

        if (!user || !user.id) {
          throw new Error('User not authenticated');
        }

        const response = await axios.post(`${API_URL}/auth/confirm-2fa`, {
          userId: user.id,
          token
        }, { withCredentials: true });

        // Update user with 2FA enabled
        if (response.data.success) {
          updateUser({ twoFactorEnabled: true });
        }

        return response.data;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Failed to confirm two-factor authentication');
        } else {
          setError('Failed to confirm two-factor authentication');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    confirmTwoFactorWithConsecutiveCodes: async (code1: string, code2: string) => {
      try {
        setLoading(true);
        clearError();

        if (!user || !user.id) {
          throw new Error('User not authenticated');
        }

        // Verify first code
        const response1 = await axios.post(`${API_URL}/auth/confirm-2fa`, {
          userId: user.id,
          token: code1
        });

        if (!response1.data.success) {
          throw new Error('First code verification failed');
        }

        // Verify second code
        const response2 = await axios.post(`${API_URL}/auth/confirm-2fa`, {
          userId: user.id,
          token: code2
        });

        // Update user with 2FA enabled
        if (response2.data.success) {
          updateUser({ twoFactorEnabled: true });
        }

        return response2.data;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Failed to verify consecutive codes');
        } else {
          setError('Failed to verify consecutive codes');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    disableTwoFactor: async (token: string) => {
      try {
        setLoading(true);
        clearError();

        if (!user || !user.id) {
          throw new Error('User not authenticated');
        }

        const response = await axios.post(`${API_URL}/auth/disable-2fa`, {
          userId: user.id,
          token
        }, { withCredentials: true });

        // Update user with 2FA disabled
        if (response.data.success) {
          updateUser({ twoFactorEnabled: false });
        }

        return response.data;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Failed to disable two-factor authentication');
        } else {
          setError('Failed to disable two-factor authentication');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    forgotPassword: async (email: string) => {
      try {
        setLoading(true);
        clearError();

        await axios.post(`${API_URL}/auth/forgot-password`, { email }, { withCredentials: true });

        // We don't need to handle the response data as the server always returns a success message
        // even if the email doesn't exist (for security reasons)
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Failed to send password reset email');
        } else {
          setError('Failed to send password reset email');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    resetPassword: async (token: string, password: string) => {
      try {
        setLoading(true);
        clearError();

        await axios.post(`${API_URL}/auth/reset-password`, { token, password }, { withCredentials: true });

        // No need to update user state as they'll need to log in again
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Password reset failed');
        } else {
          setError('Password reset failed');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    updateUser,
    clearError,
    extendSession,
    isImpersonating,
    adminId,
    impersonateUser: async (userId: string) => {
      try {
        setLoading(true);
        clearError();

        if (!user || !user.id) {
          throw new Error('User not authenticated');
        }

        // Only global admins can impersonate users
        if (!user.is_global_admin) {
          throw new Error('Only global admins can impersonate users');
        }

        const response = await axios.post(
          `${API_URL}/auth/impersonate/${userId}`,
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`
            },
            withCredentials: true
          }
        );

        // Update state with the impersonated user
        setUser(response.data.user);
        setToken(response.data.token);
        setIsImpersonating(true);
        setAdminId(response.data.adminId);

        // Store the impersonated user in localStorage
        setStorageItem('token', response.data.token);
        setStorageJSON('user', response.data.user);

        // Set the Authorization header for future requests
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

        // Reset session timeout
        resetSessionTimeout();

        return response.data;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Failed to impersonate user');
        } else {
          setError('Failed to impersonate user');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
    endImpersonation: async () => {
      try {
        setLoading(true);
        clearError();

        if (!isImpersonating) {
          throw new Error('Not currently impersonating a user');
        }

        const response = await axios.post(
          `${API_URL}/auth/end-impersonation`,
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`
            },
            withCredentials: true
          }
        );

        // Update state with the admin user
        setUser(response.data.user);
        setToken(response.data.token);
        setIsImpersonating(false);
        setAdminId(null);

        // Store the admin user in localStorage
        setStorageItem('token', response.data.token);
        setStorageJSON('user', response.data.user);

        // Set the Authorization header for future requests
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

        // Reset session timeout
        resetSessionTimeout();

        return response.data;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.error || 'Failed to end impersonation');
        } else {
          setError('Failed to end impersonation');
        }
        throw err;
      } finally {
        setLoading(false);
      }
    },
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {/* Session timeout warning dialog - only show for logged in users */}
      {user && token && showTimeoutWarning && (
        <SessionTimeoutDialog
          isOpen={true}
          onExtend={extendSession}
          onLogout={logout}
          remainingTime={remainingTime}
        />
      )}
      {children}
    </AuthContext.Provider>
  );
};
